#!/usr/bin/env python3
"""
专门调试横向边框（水平分割线）的位置
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont

def debug_horizontal_layout(pdf_path, page_num):
    """调试横向布局，测试不同的行数和上下边距"""
    
    print(f"=== 调试第{page_num}页的横向边框 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 使用已经确定的纵向边距（左9%，右4%）
            left_pct = 9
            right_pct = 4
            cols = 3
            
            # 测试不同的行数和上下边距组合
            test_configs = [
                # (name, rows, top%, bottom%)
                ("7行_上下3%", 7, 3, 3),
                ("7行_上下5%", 7, 5, 5),
                ("7行_上下8%", 7, 8, 8),
                ("7行_上4下2", 7, 4, 2),
                ("7行_上5下3", 7, 5, 3),
                ("7行_上6下4", 7, 6, 4),
                ("6行_上下3%", 6, 3, 3),
                ("6行_上下5%", 6, 5, 5),
                ("8行_上下3%", 8, 3, 3),
                ("8行_上下5%", 8, 5, 5),
                ("5行_上下5%", 5, 5, 5),
                ("9行_上下3%", 9, 3, 3),
            ]
            
            for config_name, rows, top_pct, bottom_pct in test_configs:
                print(f"\n--- 测试配置: {config_name} ---")
                print(f"行数: {rows}, 上边距: {top_pct}%, 下边距: {bottom_pct}%")
                
                # 创建网格图像
                grid_image = pil_image.copy()
                draw = ImageDraw.Draw(grid_image)
                
                # 计算内容区域
                margin_top = img_height * top_pct / 100
                margin_bottom = img_height * bottom_pct / 100
                margin_left = img_width * left_pct / 100
                margin_right = img_width * right_pct / 100
                
                content_top = margin_top
                content_bottom = img_height - margin_bottom
                content_left = margin_left
                content_right = img_width - margin_right
                
                content_width = content_right - content_left
                content_height = content_bottom - content_top
                
                # 计算单元格尺寸
                cell_width = content_width / cols
                cell_height = content_height / rows
                
                print(f"内容区域: {content_width:.0f} x {content_height:.0f} pixels")
                print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f} pixels")
                
                # 绘制内容区域边界
                draw.rectangle([content_left, content_top, content_right, content_bottom], 
                              outline='blue', width=4)
                
                # 绘制网格线
                # 垂直线（列分割）
                for i in range(cols + 1):
                    x = int(content_left + i * cell_width)
                    draw.line([(x, content_top), (x, content_bottom)], fill='red', width=3)
                
                # 水平线（行分割）- 重点关注
                for i in range(rows + 1):
                    y = int(content_top + i * cell_height)
                    draw.line([(content_left, y), (content_right, y)], fill='green', width=4)
                    # 在线旁边标注行号
                    if i < rows:
                        try:
                            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
                        except:
                            font = ImageFont.load_default()
                        draw.text((content_right + 10, y + cell_height/2), f"行{i+1}", 
                                fill='green', font=font, anchor='lm')
                
                # 添加一些单元格标签
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 14)
                except:
                    font = ImageFont.load_default()
                
                # 只标记第一列的前几行
                for row in range(min(rows, 5)):
                    col = 0
                    x = int(content_left + (col + 0.5) * cell_width)
                    y = int(content_top + (row + 0.5) * cell_height)
                    text = f"R{row+1}C{col+1}"
                    
                    bbox = draw.textbbox((x, y), text, font=font)
                    draw.rectangle(bbox, fill='yellow', outline='black', width=1)
                    draw.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # 保存网格图像
                grid_filename = f"horizontal_debug_{config_name}.png"
                grid_image.save(grid_filename)
                print(f"已保存: {grid_filename}")
                
                # 提取第一列的前3个单元格作为样本
                for row in range(min(3, rows)):
                    col = 0  # 第一列
                    
                    # 计算PDF坐标
                    pdf_margin_top = page_height * top_pct / 100
                    pdf_margin_left = page_width * left_pct / 100
                    pdf_content_width = page_width * (100 - left_pct - right_pct) / 100
                    pdf_content_height = page_height * (100 - top_pct - bottom_pct) / 100
                    
                    pdf_cell_width = pdf_content_width / cols
                    pdf_cell_height = pdf_content_height / rows
                    
                    x0 = pdf_margin_left + col * pdf_cell_width
                    y0 = pdf_margin_top + row * pdf_cell_height
                    x1 = pdf_margin_left + (col + 1) * pdf_cell_width
                    y1 = pdf_margin_top + (row + 1) * pdf_cell_height
                    
                    try:
                        # 裁剪单元格
                        cell = page.crop((x0, y0, x1, y1))
                        cell_image = cell.to_image(resolution=300)
                        cell_filename = f"cell_h_{config_name}_r{row+1}c1.png"
                        cell_image.save(cell_filename)
                        
                        print(f"  R{row+1}C1: 已保存 {cell_filename}")
                        
                    except Exception as e:
                        print(f"  R{row+1}C1: 裁剪失败 - {e}")
            
            print(f"\n=== 横向边框调试完成 ===")
            print("请查看生成的图像文件，重点关注:")
            print("1. 绿色的水平线是否与卡片的上下边框对齐")
            print("2. 每行的高度是否合适")
            print("3. 实际有多少行卡片")
            print("4. 上下边距是否合适")
            
    except Exception as e:
        print(f"横向调试失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    debug_horizontal_layout(pdf_path, page_num)

if __name__ == "__main__":
    main()
