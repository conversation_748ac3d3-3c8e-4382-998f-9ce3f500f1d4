#!/usr/bin/env python3
"""
Test the fixed grid cropping logic
"""

import pdfplumber
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def detect_content_margins(page):
    """Detect content area margins."""
    try:
        # Convert page to image for analysis
        page_image = page.to_image(resolution=150)
        pil_image = page_image.original
        
        # Convert to grayscale and numpy array
        gray_image = pil_image.convert('L')
        img_array = np.array(gray_image)
        
        # Find content boundaries (non-white areas)
        content_mask = img_array < 240
        
        # Find bounding box of content
        content_rows = np.any(content_mask, axis=1)
        content_cols = np.any(content_mask, axis=0)
        
        if np.any(content_rows) and np.any(content_cols):
            img_height, img_width = img_array.shape
            
            top = np.argmax(content_rows)
            bottom = len(content_rows) - np.argmax(content_rows[::-1]) - 1
            left = np.argmax(content_cols)
            right = len(content_cols) - np.argmax(content_cols[::-1]) - 1
            
            # Convert to PDF coordinates
            page_width = page.width
            page_height = page.height
            
            margin_top = top / img_height * page_height
            margin_bottom = (img_height - bottom) / img_height * page_height
            margin_left = left / img_width * page_width
            margin_right = (img_width - right) / img_width * page_width
            
            # Add padding
            padding = 5
            
            return {
                'top': max(0, margin_top - padding),
                'bottom': max(0, margin_bottom - padding),
                'left': max(0, margin_left - padding),
                'right': max(0, margin_right - padding)
            }
    except Exception as e:
        print(f"Margin detection failed: {e}")
    
    return {'top': 50, 'bottom': 25, 'left': 50, 'right': 30}

def test_fixed_grid_cropping(pdf_path, page_num, rows=3, cols=7):
    """Test the fixed grid cropping logic."""
    
    print(f"=== 测试修复后的网格裁剪 (第{page_num}页) ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # Get page dimensions
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.1f} x {page_height:.1f}")
            
            # Detect margins
            margins = detect_content_margins(page)
            print(f"检测到的页边距: 上{margins['top']:.1f}, 下{margins['bottom']:.1f}, "
                  f"左{margins['left']:.1f}, 右{margins['right']:.1f}")
            
            # Calculate content area
            content_left = margins['left']
            content_top = margins['top']
            content_right = page_width - margins['right']
            content_bottom = page_height - margins['bottom']
            
            content_width = content_right - content_left
            content_height = content_bottom - content_top
            
            print(f"内容区域: {content_width:.1f} x {content_height:.1f}")
            
            # Calculate cell dimensions
            cell_width = content_width / cols
            cell_height = content_height / rows
            
            print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f}")
            
            # Create visualization
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            
            # Create debug image
            debug_image = pil_image.copy()
            draw = ImageDraw.Draw(debug_image)
            
            # Convert content area to image coordinates
            img_content_left = int(content_left / page_width * img_width)
            img_content_top = int(content_top / page_height * img_height)
            img_content_right = int(content_right / page_width * img_width)
            img_content_bottom = int(content_bottom / page_height * img_height)
            
            # Draw content area boundary
            draw.rectangle([img_content_left, img_content_top, img_content_right, img_content_bottom], 
                          outline='blue', width=3)
            
            # Draw grid lines
            img_cell_width = (img_content_right - img_content_left) / cols
            img_cell_height = (img_content_bottom - img_content_top) / rows
            
            for i in range(cols + 1):
                x = int(img_content_left + i * img_cell_width)
                draw.line([(x, img_content_top), (x, img_content_bottom)], fill='red', width=2)
            
            for i in range(rows + 1):
                y = int(img_content_top + i * img_cell_height)
                draw.line([(img_content_left, y), (img_content_right, y)], fill='red', width=2)
            
            # Add cell labels
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            for row in range(rows):
                for col in range(cols):
                    x = int(img_content_left + (col + 0.5) * img_cell_width)
                    y = int(img_content_top + (row + 0.5) * img_cell_height)
                    text = f"R{row+1}C{col+1}"
                    
                    bbox = draw.textbbox((x, y), text, font=font)
                    draw.rectangle(bbox, fill='yellow', outline='black')
                    draw.text((x, y), text, fill='black', font=font, anchor='mm')
            
            # Save debug image
            debug_filename = f"test_fixed_grid_page{page_num}.png"
            debug_image.save(debug_filename)
            print(f"已保存网格图像: {debug_filename}")
            
            # Extract and save a few sample cells
            sample_cells = [(0, 1), (0, 2), (1, 1), (1, 2)]
            
            for row, col in sample_cells:
                # Calculate cell boundaries
                x0 = content_left + col * cell_width
                y0 = content_top + row * cell_height
                x1 = content_left + (col + 1) * cell_width
                y1 = content_top + (row + 1) * cell_height
                
                print(f"\n单元格 R{row+1}C{col+1}:")
                print(f"  PDF坐标: ({x0:.1f}, {y0:.1f}) -> ({x1:.1f}, {y1:.1f})")
                print(f"  尺寸: {x1-x0:.1f} x {y1-y0:.1f}")
                
                # Crop cell
                try:
                    cell = page.crop((x0, y0, x1, y1))
                    cell_image = cell.to_image(resolution=300)
                    cell_filename = f"fixed_cell_page{page_num}_r{row+1}_c{col+1}.png"
                    cell_image.save(cell_filename)
                    print(f"  已保存: {cell_filename}")
                    
                    # Try text extraction
                    cell_text = cell.extract_text()
                    print(f"  文本: '{cell_text}'")
                    
                except Exception as e:
                    print(f"  裁剪失败: {e}")
            
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    success = test_fixed_grid_cropping(pdf_path, page_num)
    
    if success:
        print(f"\n=== 测试完成 ===")
        print("请查看生成的图像文件来验证网格裁剪是否正确")
    else:
        print("测试失败")

if __name__ == "__main__":
    main()
