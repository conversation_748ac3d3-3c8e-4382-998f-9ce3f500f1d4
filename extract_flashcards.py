#!/usr/bin/env python3
"""
PDF Flashcard Extractor

This script extracts flashcard-style content from PDF files.
It can identify question-answer pairs, key terms and definitions,
and other structured learning content.
"""

import argparse
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import json

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not found. Install with: pip install PyPDF2")
    sys.exit(1)

try:
    import pdfplumber
except ImportError:
    print("pdfplumber not found. Install with: pip install pdfplumber")
    sys.exit(1)

try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    print("PIL and pytesseract not found. Install with: pip install Pillow pytesseract")
    print("Note: OCR functionality will be limited without these packages")
    Image = None
    pytesseract = None
    OCR_AVAILABLE = False


class FlashcardExtractor:
    """Extract flashcards from PDF content."""

    def __init__(self, grid_mode=False, rows=7, cols=3, auto_detect_margins=True):
        """
        Initialize the extractor.

        Args:
            grid_mode: If True, use grid-based extraction for structured layouts
            rows: Number of rows per page in grid mode (default: 7)
            cols: Number of columns per page in grid mode (default: 3)
            auto_detect_margins: If True, automatically detect content area margins
        """
        self.grid_mode = grid_mode
        self.rows = rows
        self.cols = cols
        self.auto_detect_margins = auto_detect_margins

        # Default margins optimized for 7x3 flashcard layout
        self.margins = {
            'top': 3,      # 3% of page height
            'bottom': 3,   # 3% of page height  
            'left': 9,     # 9% of page width
            'right': 4     # 4% of page width
        }

        # Common patterns for identifying flashcards
        self.qa_patterns = [
            r'Q:\s*(.+?)\s*A:\s*(.+?)(?=Q:|$)',  # Q: ... A: ... format
            r'Question:\s*(.+?)\s*Answer:\s*(.+?)(?=Question:|$)',  # Question: ... Answer: ...
            r'(\d+)\.\s*(.+?)\s*Answer:\s*(.+?)(?=\d+\.|$)',  # 1. Question Answer: ...
        ]

        self.definition_patterns = [
            r'(.+?):\s*(.+?)(?=\n[A-Z]|\n\d+\.|\n$)',  # Term: Definition
            r'\*\*(.+?)\*\*:\s*(.+?)(?=\*\*|\n$)',  # **Term**: Definition
            r'(.+?)\s*-\s*(.+?)(?=\n[A-Z]|\n$)',  # Term - Definition
        ]

        # Pattern for grid-based flashcards (word + components = result)
        self.grid_patterns = [
            r'([a-zA-Z]+)\[([^\]]+)\]',  # word[pronunciation]
            r'(\w+)\s*\+\s*(\w+)\s*\+\s*(\w+)\s*=\s*(\w+)',  # word + part + part = result
            r'(\w+)\s*\+\s*(\w+)\s*=\s*(\w+)',  # word + part = result
        ]
    
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2."""
        text = ""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text with PyPDF2: {e}")
        return text
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """Extract text using pdfplumber (better for complex layouts)."""
        text = ""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
        except Exception as e:
            print(f"Error extracting text with pdfplumber: {e}")
        return text

    def extract_grid_flashcards(self, pdf_path: str, max_pages: int = None, start_page: int = 1, use_ocr: bool = False) -> List[Dict[str, str]]:
        """Extract flashcards from grid-based PDF layout (3 rows x 7 columns)."""
        flashcards = []

        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                start_idx = start_page - 1  # Convert to 0-based index
                end_idx = start_idx + max_pages if max_pages else total_pages
                end_idx = min(end_idx, total_pages)

                print(f"总共 {total_pages} 页，将处理第 {start_page} 到第 {end_idx} 页")
                if use_ocr and OCR_AVAILABLE:
                    print("使用OCR模式处理扫描版PDF")
                elif use_ocr and not OCR_AVAILABLE:
                    print("警告: OCR依赖未安装，将使用普通文本提取")

                for page_num in range(start_idx, end_idx):
                    page = pdf.pages[page_num]
                    print(f"正在处理第 {page_num + 1} 页...")

                    # Get page dimensions
                    page_width = page.width
                    page_height = page.height

                    # Detect content margins if enabled
                    if self.auto_detect_margins:
                        current_margins = self.detect_content_margins(page)
                    else:
                        current_margins = self.margins

                    # Calculate content area (excluding margins in percentages)
                    margin_left_pts = page_width * current_margins['left'] / 100
                    margin_top_pts = page_height * current_margins['top'] / 100
                    margin_right_pts = page_width * current_margins['right'] / 100
                    margin_bottom_pts = page_height * current_margins['bottom'] / 100

                    content_left = margin_left_pts
                    content_top = margin_top_pts
                    content_right = page_width - margin_right_pts
                    content_bottom = page_height - margin_bottom_pts

                    content_width = content_right - content_left
                    content_height = content_bottom - content_top

                    print(f"内容区域: {content_width:.1f} x {content_height:.1f} (页边距已排除)")

                    # Calculate cell dimensions within content area
                    cell_width = content_width / self.cols
                    cell_height = content_height / self.rows

                    print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f}")

                    # Extract text from each cell
                    for row in range(self.rows):
                        for col in range(self.cols):
                            # Calculate cell boundaries within content area
                            x0 = content_left + col * cell_width
                            y0 = content_top + row * cell_height
                            x1 = content_left + (col + 1) * cell_width
                            y1 = content_top + (row + 1) * cell_height

                            # Try OCR first if enabled and available
                            cell_text = ""
                            if use_ocr and OCR_AVAILABLE:
                                cell_text = self.extract_cell_text_ocr(page, x0, y0, x1, y1)

                            # Fallback to regular text extraction if OCR fails or not enabled
                            if not cell_text:
                                cell = page.crop((x0, y0, x1, y1))
                                cell_text = cell.extract_text()

                            if cell_text and cell_text.strip():
                                # Parse the cell content
                                card = self.parse_grid_cell(cell_text.strip(), page_num + 1, row + 1, col + 1)
                                if card:
                                    flashcards.append(card)

        except Exception as e:
            print(f"Error extracting grid flashcards: {e}")

        return flashcards

    def extract_cell_text_ocr(self, page, x0: float, y0: float, x1: float, y1: float) -> str:
        """Extract text from a specific cell area using OCR."""
        try:
            # Convert page to image with optimized resolution for better OCR
            page_image = page.to_image(resolution=800)  # Balanced resolution for quality vs performance

            # Get the PIL image
            pil_image = page_image.original

            # Crop the cell area from the PIL image
            # Convert coordinates to image coordinates
            img_width, img_height = pil_image.size
            page_width = page.width
            page_height = page.height

            # Scale coordinates to image size
            img_x0 = int(x0 * img_width / page_width)
            img_y0 = int(y0 * img_height / page_height)
            img_x1 = int(x1 * img_width / page_width)
            img_y1 = int(y1 * img_height / page_height)

            # Crop the cell area
            cell_image = pil_image.crop((img_x0, img_y0, img_x1, img_y1))

            # Enhance image for better OCR
            cell_image = self.enhance_image_for_ocr(cell_image)

            # Try region-based OCR for better accuracy
            region_results = self.extract_by_regions(cell_image)
            if region_results:
                return region_results

            # Fallback to full cell OCR with multiple configurations
            text_results = []

            # Configuration 1: Chinese + English mixed content
            try:
                config1 = r'--oem 3 --psm 6 -l chi_sim+eng'
                text1 = pytesseract.image_to_string(cell_image, config=config1)
                if text1.strip():
                    text_results.append(('mixed', text1.strip()))
            except:
                pass

            # Configuration 2: English only (fallback)
            config2 = r'--oem 3 --psm 6 -l eng'
            text2 = pytesseract.image_to_string(cell_image, config=config2)
            if text2.strip():
                text_results.append(('eng', text2.strip()))

            # Configuration 3: Single text block (English)
            config3 = r'--oem 3 --psm 8 -l eng'
            text3 = pytesseract.image_to_string(cell_image, config=config3)
            if text3.strip():
                text_results.append(('eng_block', text3.strip()))

            # Configuration 4: Chinese + English with different PSM
            try:
                config4 = r'--oem 3 --psm 8 -l chi_sim+eng'
                text4 = pytesseract.image_to_string(cell_image, config=config4)
                if text4.strip():
                    text_results.append(('mixed_block', text4.strip()))
            except:
                pass

            # Choose the best result based on content richness
            if text_results:
                best_text = self.select_best_ocr_result(text_results)
                return best_text

            return ""
        except Exception as e:
            print(f"OCR extraction failed: {e}")
            return ""

    def select_best_ocr_result(self, text_results):
        """Select the best OCR result from multiple configurations."""
        if not text_results:
            return ""

        # Score each result
        scored_results = []
        for config_type, text in text_results:
            score = self.score_ocr_result(text, config_type)
            scored_results.append((score, text))

        # Return the highest scoring result
        scored_results.sort(key=lambda x: x[0], reverse=True)
        return scored_results[0][1]

    def score_ocr_result(self, text, config_type):
        """Score OCR result based on content quality."""
        score = 0

        # Base score for having content
        if text.strip():
            score += 10

        # Bonus for mixed language content (likely more complete)
        if config_type.startswith('mixed'):
            score += 20

        # Count meaningful content
        english_words = len(re.findall(r'\b[a-zA-Z]{3,}\b', text))
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))

        score += english_words * 5
        score += chinese_chars * 3

        # Bonus for phonetic symbols
        if re.search(r'[\[\(][^\]\)]*[\]\)]', text):
            score += 15

        # Bonus for word formation patterns
        if re.search(r'[a-zA-Z]+\s*\+\s*[a-zA-Z]+\s*[=\+]\s*[a-zA-Z]+', text):
            score += 25

        # Penalty for too many special characters (OCR noise)
        special_chars = len(re.findall(r'[^\w\s\[\]\(\)\+\=\-\.:;,，。：；]', text))
        score -= special_chars * 2

        return score

    def enhance_image_for_ocr(self, image):
        """Enhanced image processing for better OCR recognition."""
        try:
            from PIL import ImageEnhance, ImageFilter, ImageOps
            import numpy as np

            # Convert to grayscale if not already
            if image.mode != 'L':
                image = image.convert('L')

            # Ensure minimum size for OCR (at least 300px width)
            width, height = image.size
            if width < 300:
                scale_factor = 300 / width
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to numpy for advanced processing
            img_array = np.array(image)

            # Adaptive histogram equalization for better contrast
            from PIL import ImageOps
            image = ImageOps.equalize(image)
            img_array = np.array(image)

            # Apply adaptive thresholding instead of fixed threshold
            # Use Otsu's method for automatic threshold selection
            from scipy import ndimage
            try:
                # Calculate optimal threshold using Otsu's method
                hist, bin_edges = np.histogram(img_array, bins=256, range=(0, 256))
                hist = hist.astype(float)

                # Otsu's method
                total_pixels = img_array.size
                current_max, threshold = 0, 0
                sum_total, sum_foreground = 0, 0
                weight_background, weight_foreground = 0, 0

                for i in range(256):
                    sum_total += i * hist[i]

                for i in range(256):
                    weight_background += hist[i]
                    if weight_background == 0:
                        continue

                    weight_foreground = total_pixels - weight_background
                    if weight_foreground == 0:
                        break

                    sum_foreground += i * hist[i]
                    mean_background = sum_foreground / weight_background
                    mean_foreground = (sum_total - sum_foreground) / weight_foreground

                    variance_between = weight_background * weight_foreground * (mean_background - mean_foreground) ** 2

                    if variance_between > current_max:
                        current_max = variance_between
                        threshold = i

                # Apply the calculated threshold
                img_array = np.where(img_array > threshold, 255, 0).astype(np.uint8)

            except:
                # Fallback to simple thresholding
                threshold = np.mean(img_array)
                img_array = np.where(img_array > threshold, 255, 0).astype(np.uint8)

            # Convert back to PIL Image
            image = Image.fromarray(img_array)

            # Apply morphological operations to clean up noise
            # Opening operation (erosion followed by dilation)
            image = image.filter(ImageFilter.MinFilter(3))  # Erosion
            image = image.filter(ImageFilter.MaxFilter(3))  # Dilation

            # Final sharpening
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.5)

            return image
        except Exception as e:
            print(f"Enhanced image processing failed, using basic enhancement: {e}")
            # Fallback to basic enhancement
            return self.basic_enhance_image(image)

    def basic_enhance_image(self, image):
        """Basic image enhancement as fallback."""
        try:
            from PIL import ImageEnhance, ImageFilter

            if image.mode != 'L':
                image = image.convert('L')

            # Basic contrast and sharpness enhancement
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)

            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.5)

            return image
        except Exception as e:
            print(f"Basic image enhancement failed: {e}")
            return image

    def extract_by_regions(self, cell_image):
        """Extract text by dividing cell into regions based on flashcard structure."""
        try:
            width, height = cell_image.size

            # Define regions based on typical flashcard layout
            regions = {
                'top': (0, 0, width, int(height * 0.25)),           # Main word + phonetics
                'upper_mid': (0, int(height * 0.25), width, int(height * 0.45)),  # Chinese definition
                'lower_mid': (0, int(height * 0.45), width, int(height * 0.75)),  # Word formation
                'bottom': (0, int(height * 0.75), width, height)    # Mnemonic
            }

            extracted_parts = {}

            for region_name, (x1, y1, x2, y2) in regions.items():
                try:
                    # Crop region
                    region_img = cell_image.crop((x1, y1, x2, y2))

                    # Skip very small regions
                    if region_img.size[0] < 50 or region_img.size[1] < 20:
                        continue

                    # Apply region-specific OCR
                    if region_name == 'top':
                        # Main word and phonetics - prioritize English
                        configs = [
                            r'--oem 3 --psm 8 -l eng',
                            r'--oem 3 --psm 6 -l chi_sim+eng'
                        ]
                    elif region_name == 'upper_mid':
                        # Chinese definition - prioritize Chinese
                        configs = [
                            r'--oem 3 --psm 6 -l chi_sim',
                            r'--oem 3 --psm 6 -l chi_sim+eng'
                        ]
                    elif region_name == 'lower_mid':
                        # Word formation - mixed content
                        configs = [
                            r'--oem 3 --psm 6 -l chi_sim+eng',
                            r'--oem 3 --psm 8 -l eng'
                        ]
                    else:  # bottom
                        # Mnemonic - Chinese
                        configs = [
                            r'--oem 3 --psm 6 -l chi_sim',
                            r'--oem 3 --psm 6 -l chi_sim+eng'
                        ]

                    # Try each configuration
                    best_text = ""
                    best_score = 0

                    for config in configs:
                        try:
                            text = pytesseract.image_to_string(region_img, config=config)
                            text = text.strip()
                            if text:
                                score = self.score_region_text(text, region_name)
                                if score > best_score:
                                    best_score = score
                                    best_text = text
                        except:
                            continue

                    if best_text:
                        extracted_parts[region_name] = best_text

                except Exception as e:
                    print(f"Region {region_name} extraction failed: {e}")
                    continue

            # Combine regions into full text if we got good results
            if len(extracted_parts) >= 2:
                combined_text = ""
                for region in ['top', 'upper_mid', 'lower_mid', 'bottom']:
                    if region in extracted_parts:
                        combined_text += extracted_parts[region] + " "
                return combined_text.strip()

            return None

        except Exception as e:
            print(f"Region-based extraction failed: {e}")
            return None

    def score_region_text(self, text, region_name):
        """Score text based on expected content for each region."""
        score = 0

        if not text.strip():
            return 0

        # Base score for having content
        score += 10

        if region_name == 'top':
            # Expect English words and phonetic symbols
            english_words = len(re.findall(r'\b[a-zA-Z]{3,}\b', text))
            phonetic_symbols = len(re.findall(r'[\[\(][^\]\)]*[\]\)]', text))
            score += english_words * 10
            score += phonetic_symbols * 15

        elif region_name == 'upper_mid':
            # Expect Chinese characters and part-of-speech markers
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            pos_markers = len(re.findall(r'[vn]\\.', text))
            score += chinese_chars * 5
            score += pos_markers * 20

        elif region_name == 'lower_mid':
            # Expect word formation patterns
            formation_patterns = len(re.findall(r'[a-zA-Z]+\s*\+\s*[a-zA-Z]+', text))
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            score += formation_patterns * 25
            score += chinese_chars * 3

        elif region_name == 'bottom':
            # Expect Chinese mnemonic
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            colon_patterns = len(re.findall(r'[\u4e00-\u9fff]+：[\u4e00-\u9fff]+', text))
            score += chinese_chars * 5
            score += colon_patterns * 30

        # Penalty for too many special characters (OCR noise)
        special_chars = len(re.findall(r'[^\w\s\[\]\(\)\+\=\-\.:;,，。：；]', text))
        score -= special_chars * 2

        return max(0, score)

    def detect_content_margins(self, page):
        """Automatically detect content area margins by analyzing page content."""
        try:
            # Convert page to image for analysis
            page_image = page.to_image(resolution=150)  # Lower resolution for margin detection
            pil_image = page_image.original

            # Convert to grayscale and numpy array
            import numpy as np
            gray_image = pil_image.convert('L')
            img_array = np.array(gray_image)

            # Find content boundaries (non-white areas)
            content_mask = img_array < 240  # Threshold for non-white content

            # Find bounding box of content
            content_rows = np.any(content_mask, axis=1)
            content_cols = np.any(content_mask, axis=0)

            if np.any(content_rows) and np.any(content_cols):
                img_height, img_width = img_array.shape

                top = np.argmax(content_rows)
                bottom = len(content_rows) - np.argmax(content_rows[::-1]) - 1
                left = np.argmax(content_cols)
                right = len(content_cols) - np.argmax(content_cols[::-1]) - 1

                # Convert to PDF coordinates
                page_width = page.width
                page_height = page.height

                margin_top = top / img_height * page_height
                margin_bottom = (img_height - bottom) / img_height * page_height
                margin_left = left / img_width * page_width
                margin_right = (img_width - right) / img_width * page_width

                # Convert to percentages and add some padding
                padding_pct = 1  # 1% padding

                detected_margins = {
                    'top': max(0, margin_top / page_height * 100 - padding_pct),
                    'bottom': max(0, margin_bottom / page_height * 100 - padding_pct),
                    'left': max(0, margin_left / page_width * 100 - padding_pct),
                    'right': max(0, margin_right / page_width * 100 - padding_pct)
                }

                print(f"检测到的页边距: 上{detected_margins['top']:.1f}%, 下{detected_margins['bottom']:.1f}%, "
                      f"左{detected_margins['left']:.1f}%, 右{detected_margins['right']:.1f}%")

                return detected_margins
            else:
                print("未能检测到内容边界，使用默认页边距")
                return self.margins

        except Exception as e:
            print(f"页边距检测失败，使用默认值: {e}")
            return self.margins

    def clean_ocr_text(self, text: str) -> str:
        """Clean OCR text to improve accuracy."""
        # Remove common OCR artifacts
        text = re.sub(r'[|\\\/\[\]{}()]+', ' ', text)  # Remove brackets and separators
        text = re.sub(r'[^\w\s\+\=\-\[\]\'\"\.,:;!?]', ' ', text)  # Keep only letters, numbers, basic punctuation
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        text = text.strip()

        # Fix common OCR mistakes
        replacements = {
            'l': 'I',  # lowercase l often mistaken for I
            '0': 'O',  # zero often mistaken for O in words
            '1': 'l',  # 1 often mistaken for l
            '5': 'S',  # 5 often mistaken for S
        }

        # Apply replacements only in word contexts
        words = text.split()
        cleaned_words = []
        for word in words:
            if re.match(r'^[a-zA-Z]+$', word):  # Only for pure letter words
                for old, new in replacements.items():
                    if old in word and len(word) > 2:
                        word = word.replace(old, new)
            cleaned_words.append(word)

        return ' '.join(cleaned_words)

    def extract_vocabulary_word(self, text: str) -> Optional[str]:
        """Extract the main vocabulary word from text."""
        # Clean the text first
        text = self.clean_ocr_text(text)

        # Look for words in brackets (pronunciation indicators)
        bracket_match = re.search(r'([a-zA-Z]{3,})\s*\[', text)
        if bracket_match:
            return bracket_match.group(1).lower()

        # Look for common English word patterns at the beginning
        # Many flashcards start with the target word
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        if words:
            # Filter out common OCR artifacts and short words
            common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'adj', 'adv', 'noun', 'verb', 'prep'}

            # Try to find the best candidate word
            candidates = []
            for word in words:
                word_lower = word.lower()
                if (len(word) >= 4 and
                    word_lower not in common_words and
                    not re.match(r'^[0-9]+$', word) and  # Not just numbers
                    not re.match(r'^[A-Z]{2,}$', word)):  # Not all caps (likely OCR artifacts)
                    candidates.append(word_lower)

            if candidates:
                # Prefer words that look like real English words
                for candidate in candidates:
                    if self.looks_like_english_word(candidate):
                        return candidate
                # If no clear English word, return the first candidate
                return candidates[0]

        return None

    def looks_like_english_word(self, word: str) -> bool:
        """Check if a word looks like a real English word."""
        # Basic heuristics for English words
        if len(word) < 3:
            return False

        # Check for reasonable vowel/consonant distribution
        vowels = set('aeiou')
        vowel_count = sum(1 for c in word if c in vowels)
        consonant_count = len(word) - vowel_count

        # Should have at least one vowel and reasonable distribution
        if vowel_count == 0 or vowel_count > len(word) * 0.7:
            return False

        # Check for common English patterns
        common_prefixes = ['un', 're', 'in', 'dis', 'en', 'non', 'pre', 'pro', 'anti', 'de', 'over', 'under', 'out', 'up']
        common_suffixes = ['ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment', 'ful', 'less', 'able', 'ible', 'ous', 'ive', 'al', 'ic', 'ary', 'ory']

        # Bonus points for common patterns
        for prefix in common_prefixes:
            if word.startswith(prefix) and len(word) > len(prefix) + 2:
                return True

        for suffix in common_suffixes:
            if word.endswith(suffix) and len(word) > len(suffix) + 2:
                return True

        # Check for double letters (common in English)
        for i in range(len(word) - 1):
            if word[i] == word[i + 1] and word[i] in 'llssttffmmnnpprrdd':
                return True

        return True  # Default to true if no red flags

    def parse_flashcard_content(self, cell_text: str) -> Dict[str, str]:
        """Parse structured flashcard content based on the advance card format."""
        result = {
            'word': '',
            'pronunciation_uk': '',
            'pronunciation_us': '',
            'definition': '',
            'word_formation': '',
            'chinese_components': '',
            'mnemonic': ''
        }

        # Extract main word and pronunciations
        # Pattern: word[uk_pronunciation](US)us_pronunciation
        word_pattern = r'([a-zA-Z]+)\s*\[([^\]]+)\]\s*(?:\(US\)\s*([^\s\]]+))?'
        word_match = re.search(word_pattern, cell_text)
        if word_match:
            result['word'] = word_match.group(1).lower()
            result['pronunciation_uk'] = word_match.group(2)
            if word_match.group(3):
                result['pronunciation_us'] = word_match.group(3)

        # Extract Chinese definition (v.推进，促进；前进)
        chinese_def_pattern = r'[vn]\.\s*([^a-zA-Z\+\=]+)'
        chinese_def_match = re.search(chinese_def_pattern, cell_text)
        if chinese_def_match:
            result['definition'] = chinese_def_match.group(1).strip('，。；：')

        # Extract word formation pattern (ad + van + ce = advance)
        formation_pattern = r'([a-zA-Z]+)\s*\+\s*([a-zA-Z]+)\s*\+\s*([a-zA-Z]+)\s*=\s*([a-zA-Z]+)'
        formation_match = re.search(formation_pattern, cell_text)
        if formation_match:
            components = [formation_match.group(1), formation_match.group(2), formation_match.group(3)]
            result_word = formation_match.group(4)
            result['word_formation'] = f"{' + '.join(components)} = {result_word}"

        # Extract Chinese component mapping (阿弟 + 玩 + 车 = 推进)
        chinese_pattern = r'([\u4e00-\u9fff]+)\s*\+\s*([\u4e00-\u9fff]+)\s*\+\s*([\u4e00-\u9fff]+)\s*=\s*([\u4e00-\u9fff]+)'
        chinese_match = re.search(chinese_pattern, cell_text)
        if chinese_match:
            chinese_components = [chinese_match.group(1), chinese_match.group(2), chinese_match.group(3)]
            chinese_result = chinese_match.group(4)
            result['chinese_components'] = f"{' + '.join(chinese_components)} = {chinese_result}"

        # Extract mnemonic (阿弟玩车：推它前进)
        mnemonic_pattern = r'([\u4e00-\u9fff]+)：([\u4e00-\u9fff]+)'
        mnemonic_match = re.search(mnemonic_pattern, cell_text)
        if mnemonic_match:
            result['mnemonic'] = f"{mnemonic_match.group(1)}：{mnemonic_match.group(2)}"

        return result

    def parse_grid_cell(self, cell_text: str, page: int, row: int, col: int) -> Optional[Dict[str, str]]:
        """Parse individual cell content to extract flashcard information."""
        # Clean the text
        original_text = cell_text
        cell_text = self.clean_ocr_text(cell_text)

        # Skip empty cells or cells with only numbers/symbols
        if len(cell_text) < 3:
            return None

        # Skip cells that are likely page numbers or headers
        if re.match(r'^-?\d+\-?$', cell_text.strip()):
            return None

        # Try to parse as structured flashcard content
        parsed_content = self.parse_flashcard_content(cell_text)

        # If we found a main word, create a comprehensive vocabulary card
        if parsed_content['word']:
            return {
                'type': 'vocabulary_complete',
                'word': parsed_content['word'],
                'pronunciation_uk': parsed_content['pronunciation_uk'],
                'pronunciation_us': parsed_content['pronunciation_us'],
                'definition': parsed_content['definition'],
                'word_formation': parsed_content['word_formation'],
                'chinese_components': parsed_content['chinese_components'],
                'mnemonic': parsed_content['mnemonic'],
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        # Try to extract word and pronunciation pattern: word[pronunciation]
        word_pronunciation_match = re.search(r'([a-zA-Z]{3,})\s*\[([^\]]+)\]', cell_text)
        if word_pronunciation_match:
            word = word_pronunciation_match.group(1).lower()
            pronunciation = word_pronunciation_match.group(2)

            # Extract the rest as definition/explanation
            remaining_text = cell_text.replace(word_pronunciation_match.group(0), '').strip()

            return {
                'type': 'vocabulary',
                'word': word,
                'pronunciation': pronunciation,
                'definition': remaining_text,
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        # Try to extract word formation patterns
        # Pattern 1: word + part + part = result
        formation_3_match = re.search(r'([a-zA-Z]+)\s*\+\s*([a-zA-Z]+)\s*\+\s*([a-zA-Z]+)\s*[=_]\s*([a-zA-Z]+)', cell_text)
        if formation_3_match:
            components = [formation_3_match.group(1), formation_3_match.group(2), formation_3_match.group(3)]
            result = formation_3_match.group(4)
            return {
                'type': 'word_formation',
                'components': components,
                'result': result.lower(),
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        # Pattern 2: word + part = result
        formation_2_match = re.search(r'([a-zA-Z]+)\s*\+\s*([a-zA-Z]+)\s*[=_]\s*([a-zA-Z]+)', cell_text)
        if formation_2_match:
            components = [formation_2_match.group(1), formation_2_match.group(2)]
            result = formation_2_match.group(3)
            return {
                'type': 'word_formation',
                'components': components,
                'result': result.lower(),
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        # Extract main vocabulary word
        main_word = self.extract_vocabulary_word(cell_text)
        if main_word:
            # Extract definition (everything except the main word)
            definition_parts = []
            words = cell_text.split()
            for word in words:
                if word.lower() != main_word and not re.match(r'^[+\-=_]+$', word):
                    definition_parts.append(word)

            definition = ' '.join(definition_parts).strip()

            return {
                'type': 'vocabulary',
                'word': main_word,
                'pronunciation': '',
                'definition': definition,
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        # If no specific pattern matches but has meaningful content, treat as general
        if len(cell_text) > 5 and re.search(r'[a-zA-Z]', cell_text):
            return {
                'type': 'general',
                'content': cell_text,
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': original_text
            }

        return None
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove page numbers and headers/footers (basic patterns)
        text = re.sub(r'\n\d+\n', '\n', text)
        text = re.sub(r'\nPage \d+\n', '\n', text)
        return text.strip()
    
    def extract_qa_pairs(self, text: str) -> List[Dict[str, str]]:
        """Extract question-answer pairs from text."""
        flashcards = []
        
        for pattern in self.qa_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if len(match.groups()) >= 2:
                    question = match.group(1).strip()
                    answer = match.group(2).strip()
                    if question and answer:
                        flashcards.append({
                            'type': 'qa',
                            'question': question,
                            'answer': answer
                        })
        
        return flashcards
    
    def extract_definitions(self, text: str) -> List[Dict[str, str]]:
        """Extract term-definition pairs from text."""
        flashcards = []
        
        for pattern in self.definition_patterns:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                if len(match.groups()) >= 2:
                    term = match.group(1).strip()
                    definition = match.group(2).strip()
                    # Filter out very short or very long terms/definitions
                    if 2 <= len(term) <= 100 and 5 <= len(definition) <= 500:
                        flashcards.append({
                            'type': 'definition',
                            'term': term,
                            'definition': definition
                        })
        
        return flashcards
    
    def extract_bullet_points(self, text: str) -> List[Dict[str, str]]:
        """Extract bullet points that could be flashcards."""
        flashcards = []
        
        # Look for bullet points or numbered lists
        bullet_pattern = r'[•\-\*]\s*(.+?)(?=\n[•\-\*]|\n\n|\n$)'
        numbered_pattern = r'\d+\.\s*(.+?)(?=\n\d+\.|\n\n|\n$)'
        
        for pattern in [bullet_pattern, numbered_pattern]:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                content = match.group(1).strip()
                if 10 <= len(content) <= 200:  # Reasonable length for a flashcard
                    flashcards.append({
                        'type': 'bullet',
                        'content': content
                    })
        
        return flashcards
    
    def extract_flashcards(self, pdf_path: str, method: str = 'pdfplumber', max_pages: int = None, start_page: int = 1, use_ocr: bool = False) -> List[Dict[str, str]]:
        """Main method to extract flashcards from PDF."""
        print(f"从 {pdf_path} 提取闪卡...")

        # Use grid-based extraction if enabled
        if self.grid_mode:
            return self.extract_grid_flashcards(pdf_path, max_pages, start_page, use_ocr)

        # Extract text using traditional method
        if method == 'pypdf2':
            text = self.extract_text_pypdf2(pdf_path)
        else:
            text = self.extract_text_pdfplumber(pdf_path)

        if not text:
            print("未从PDF中提取到文本")
            return []

        # Clean text
        text = self.clean_text(text)

        # Extract different types of flashcards
        flashcards = []
        flashcards.extend(self.extract_qa_pairs(text))
        flashcards.extend(self.extract_definitions(text))
        flashcards.extend(self.extract_bullet_points(text))

        # Remove duplicates
        seen = set()
        unique_flashcards = []
        for card in flashcards:
            card_str = str(sorted(card.items()))
            if card_str not in seen:
                seen.add(card_str)
                unique_flashcards.append(card)

        return unique_flashcards
    
    def save_flashcards(self, flashcards: List[Dict[str, str]], output_path: str, format: str = 'json'):
        """Save flashcards to file."""
        if format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(flashcards, f, indent=2, ensure_ascii=False)
        elif format == 'txt':
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, card in enumerate(flashcards, 1):
                    f.write(f"=== 闪卡 {i} ===\n")
                    if card['type'] == 'qa':
                        f.write(f"问题: {card['question']}\n")
                        f.write(f"答案: {card['answer']}\n")
                    elif card['type'] == 'definition':
                        f.write(f"术语: {card['term']}\n")
                        f.write(f"定义: {card['definition']}\n")
                    elif card['type'] == 'bullet':
                        f.write(f"内容: {card['content']}\n")
                    elif card['type'] == 'vocabulary':
                        f.write(f"单词: {card['word']}\n")
                        f.write(f"发音: {card['pronunciation']}\n")
                        f.write(f"定义: {card['definition']}\n")
                        f.write(f"位置: {card['position']}\n")
                    elif card['type'] == 'vocabulary_complete':
                        f.write(f"单词: {card['word']}\n")
                        if card['pronunciation_uk']:
                            f.write(f"英式发音: {card['pronunciation_uk']}\n")
                        if card['pronunciation_us']:
                            f.write(f"美式发音: {card['pronunciation_us']}\n")
                        if card['definition']:
                            f.write(f"中文释义: {card['definition']}\n")
                        if card['word_formation']:
                            f.write(f"词根构成: {card['word_formation']}\n")
                        if card['chinese_components']:
                            f.write(f"中文对应: {card['chinese_components']}\n")
                        if card['mnemonic']:
                            f.write(f"记忆口诀: {card['mnemonic']}\n")
                        f.write(f"位置: {card['position']}\n")
                    elif card['type'] == 'word_formation':
                        components_str = ' + '.join(card['components'])
                        f.write(f"构词: {components_str} = {card['result']}\n")
                        f.write(f"位置: {card['position']}\n")
                    elif card['type'] == 'general':
                        f.write(f"内容: {card['content']}\n")
                        f.write(f"位置: {card['position']}\n")
                    f.write("\n")

        print(f"已保存 {len(flashcards)} 张闪卡到 {output_path}")


def main():
    parser = argparse.ArgumentParser(description='从PDF文件中提取闪卡')
    parser.add_argument('pdf_path', help='PDF文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径 (默认: flashcards.json)')
    parser.add_argument('-f', '--format', choices=['json', 'txt'], default='json',
                       help='输出格式 (默认: json)')
    parser.add_argument('-m', '--method', choices=['pypdf2', 'pdfplumber'], default='pdfplumber',
                       help='文本提取方法 (默认: pdfplumber)')
    parser.add_argument('-g', '--grid', action='store_true',
                       help='使用网格模式提取 (适用于3行7列的规整布局)')
    parser.add_argument('--rows', type=int, default=7,
                       help='网格模式下每页的行数 (默认: 7)')
    parser.add_argument('--cols', type=int, default=3,
                       help='网格模式下每页的列数 (默认: 3)')
    parser.add_argument('--max-pages', type=int, default=None,
                       help='限制处理的页数 (用于测试)')
    parser.add_argument('--start-page', type=int, default=1,
                       help='开始处理的页码 (默认: 1)')
    parser.add_argument('--ocr', action='store_true',
                       help='使用OCR处理扫描版PDF (需要安装tesseract)')

    args = parser.parse_args()

    # Validate input file
    if not Path(args.pdf_path).exists():
        print(f"错误: 文件 {args.pdf_path} 不存在")
        sys.exit(1)

    # Set output path
    if args.output:
        output_path = args.output
    else:
        base_name = Path(args.pdf_path).stem
        extension = 'json' if args.format == 'json' else 'txt'
        output_path = f"{base_name}_flashcards.{extension}"

    # Extract flashcards
    extractor = FlashcardExtractor(grid_mode=args.grid, rows=args.rows, cols=args.cols)
    flashcards = extractor.extract_flashcards(
        args.pdf_path,
        args.method,
        getattr(args, 'max_pages', None),
        getattr(args, 'start_page', 1),
        getattr(args, 'ocr', False)
    )

    if flashcards:
        extractor.save_flashcards(flashcards, output_path, args.format)
        print(f"成功提取了 {len(flashcards)} 张闪卡")

        # Print summary by type
        type_counts = {}
        for card in flashcards:
            card_type = card.get('type', 'unknown')
            type_counts[card_type] = type_counts.get(card_type, 0) + 1

        print("闪卡类型统计:")
        for card_type, count in type_counts.items():
            print(f"  {card_type}: {count} 张")
    else:
        print("在PDF中未找到闪卡")


if __name__ == "__main__":
    main()
