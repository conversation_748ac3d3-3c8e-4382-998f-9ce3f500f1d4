#!/usr/bin/env python3
"""
PDF Flashcard Extractor

This script extracts flashcard-style content from PDF files.
It can identify question-answer pairs, key terms and definitions,
and other structured learning content.
"""

import argparse
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import json

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not found. Install with: pip install PyPDF2")
    sys.exit(1)

try:
    import pdfplumber
except ImportError:
    print("pdfplumber not found. Install with: pip install pdfplumber")
    sys.exit(1)

try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    print("PIL and pytesseract not found. Install with: pip install Pillow pytesseract")
    print("Note: OCR functionality will be limited without these packages")
    Image = None
    pytesseract = None
    OCR_AVAILABLE = False


class FlashcardExtractor:
    """Extract flashcards from PDF content."""

    def __init__(self, grid_mode=False, rows=3, cols=7):
        """
        Initialize the extractor.

        Args:
            grid_mode: If True, use grid-based extraction for structured layouts
            rows: Number of rows per page in grid mode
            cols: Number of columns per page in grid mode
        """
        self.grid_mode = grid_mode
        self.rows = rows
        self.cols = cols

        # Common patterns for identifying flashcards
        self.qa_patterns = [
            r'Q:\s*(.+?)\s*A:\s*(.+?)(?=Q:|$)',  # Q: ... A: ... format
            r'Question:\s*(.+?)\s*Answer:\s*(.+?)(?=Question:|$)',  # Question: ... Answer: ...
            r'(\d+)\.\s*(.+?)\s*Answer:\s*(.+?)(?=\d+\.|$)',  # 1. Question Answer: ...
        ]

        self.definition_patterns = [
            r'(.+?):\s*(.+?)(?=\n[A-Z]|\n\d+\.|\n$)',  # Term: Definition
            r'\*\*(.+?)\*\*:\s*(.+?)(?=\*\*|\n$)',  # **Term**: Definition
            r'(.+?)\s*-\s*(.+?)(?=\n[A-Z]|\n$)',  # Term - Definition
        ]

        # Pattern for grid-based flashcards (word + components = result)
        self.grid_patterns = [
            r'([a-zA-Z]+)\[([^\]]+)\]',  # word[pronunciation]
            r'(\w+)\s*\+\s*(\w+)\s*\+\s*(\w+)\s*=\s*(\w+)',  # word + part + part = result
            r'(\w+)\s*\+\s*(\w+)\s*=\s*(\w+)',  # word + part = result
        ]
    
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2."""
        text = ""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text with PyPDF2: {e}")
        return text
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """Extract text using pdfplumber (better for complex layouts)."""
        text = ""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
        except Exception as e:
            print(f"Error extracting text with pdfplumber: {e}")
        return text

    def extract_grid_flashcards(self, pdf_path: str, max_pages: int = None, start_page: int = 1, use_ocr: bool = False) -> List[Dict[str, str]]:
        """Extract flashcards from grid-based PDF layout (3 rows x 7 columns)."""
        flashcards = []

        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                start_idx = start_page - 1  # Convert to 0-based index
                end_idx = start_idx + max_pages if max_pages else total_pages
                end_idx = min(end_idx, total_pages)

                print(f"总共 {total_pages} 页，将处理第 {start_page} 到第 {end_idx} 页")
                if use_ocr and OCR_AVAILABLE:
                    print("使用OCR模式处理扫描版PDF")
                elif use_ocr and not OCR_AVAILABLE:
                    print("警告: OCR依赖未安装，将使用普通文本提取")

                for page_num in range(start_idx, end_idx):
                    page = pdf.pages[page_num]
                    print(f"正在处理第 {page_num + 1} 页...")

                    # Get page dimensions
                    page_width = page.width
                    page_height = page.height

                    # Calculate cell dimensions
                    cell_width = page_width / self.cols
                    cell_height = page_height / self.rows

                    # Extract text from each cell
                    for row in range(self.rows):
                        for col in range(self.cols):
                            # Calculate cell boundaries
                            x0 = col * cell_width
                            y0 = row * cell_height
                            x1 = (col + 1) * cell_width
                            y1 = (row + 1) * cell_height

                            # Try OCR first if enabled and available
                            cell_text = ""
                            if use_ocr and OCR_AVAILABLE:
                                cell_text = self.extract_cell_text_ocr(page, x0, y0, x1, y1)

                            # Fallback to regular text extraction if OCR fails or not enabled
                            if not cell_text:
                                cell = page.crop((x0, y0, x1, y1))
                                cell_text = cell.extract_text()

                            if cell_text and cell_text.strip():
                                # Parse the cell content
                                card = self.parse_grid_cell(cell_text.strip(), page_num + 1, row + 1, col + 1)
                                if card:
                                    flashcards.append(card)

        except Exception as e:
            print(f"Error extracting grid flashcards: {e}")

        return flashcards

    def extract_cell_text_ocr(self, page, x0: float, y0: float, x1: float, y1: float) -> str:
        """Extract text from a specific cell area using OCR."""
        try:
            # Convert page to image with high resolution for better OCR
            page_image = page.to_image(resolution=300)

            # Get the PIL image
            pil_image = page_image.original

            # Crop the cell area from the PIL image
            # Convert coordinates to image coordinates
            img_width, img_height = pil_image.size
            page_width = page.width
            page_height = page.height

            # Scale coordinates to image size
            img_x0 = int(x0 * img_width / page_width)
            img_y0 = int(y0 * img_height / page_height)
            img_x1 = int(x1 * img_width / page_width)
            img_y1 = int(y1 * img_height / page_height)

            # Crop the cell area
            cell_image = pil_image.crop((img_x0, img_y0, img_x1, img_y1))

            # Use pytesseract to extract text
            # Configure for English (will add Chinese support later)
            custom_config = r'--oem 3 --psm 6 -l eng'
            text = pytesseract.image_to_string(cell_image, config=custom_config)

            return text.strip()
        except Exception as e:
            print(f"OCR extraction failed: {e}")
            return ""

    def parse_grid_cell(self, cell_text: str, page: int, row: int, col: int) -> Optional[Dict[str, str]]:
        """Parse individual cell content to extract flashcard information."""
        # Clean the text
        cell_text = re.sub(r'\s+', ' ', cell_text).strip()

        # Skip empty cells or cells with only numbers/symbols
        if len(cell_text) < 3 or cell_text.isdigit():
            return None

        # Extract main English words (likely the vocabulary words)
        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', cell_text)
        main_word = None
        if english_words:
            # Find the longest word as it's likely the main vocabulary word
            main_word = max(english_words, key=len)

        # Try to extract word and pronunciation
        word_match = re.search(r'([a-zA-Z]+)\[([^\]]+)\]', cell_text)
        if word_match:
            word = word_match.group(1)
            pronunciation = word_match.group(2)

            # Extract the rest as definition/explanation
            remaining_text = cell_text.replace(word_match.group(0), '').strip()

            return {
                'type': 'vocabulary',
                'word': word,
                'pronunciation': pronunciation,
                'definition': remaining_text,
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': cell_text
            }

        # Try to extract word formation pattern (word + part + part = result)
        formation_match = re.search(r'(\w+)\s*\+\s*(\w+)\s*\+\s*(\w+)\s*[=_]\s*(\w+)', cell_text)
        if formation_match:
            return {
                'type': 'word_formation',
                'components': [formation_match.group(1), formation_match.group(2), formation_match.group(3)],
                'result': formation_match.group(4),
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': cell_text
            }

        # Try to extract simple word formation (word + part = result)
        simple_formation_match = re.search(r'(\w+)\s*\+\s*(\w+)\s*[=_]\s*(\w+)', cell_text)
        if simple_formation_match:
            return {
                'type': 'word_formation',
                'components': [simple_formation_match.group(1), simple_formation_match.group(2)],
                'result': simple_formation_match.group(3),
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': cell_text
            }

        # If we found a main English word, create a vocabulary card
        if main_word and len(main_word) >= 4:
            return {
                'type': 'vocabulary',
                'word': main_word,
                'pronunciation': '',
                'definition': cell_text.replace(main_word, '').strip(),
                'page': page,
                'position': f"Row {row}, Col {col}",
                'raw_text': cell_text
            }

        # If no specific pattern matches, treat as general content
        return {
            'type': 'general',
            'content': cell_text,
            'page': page,
            'position': f"Row {row}, Col {col}",
            'raw_text': cell_text
        }
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove page numbers and headers/footers (basic patterns)
        text = re.sub(r'\n\d+\n', '\n', text)
        text = re.sub(r'\nPage \d+\n', '\n', text)
        return text.strip()
    
    def extract_qa_pairs(self, text: str) -> List[Dict[str, str]]:
        """Extract question-answer pairs from text."""
        flashcards = []
        
        for pattern in self.qa_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if len(match.groups()) >= 2:
                    question = match.group(1).strip()
                    answer = match.group(2).strip()
                    if question and answer:
                        flashcards.append({
                            'type': 'qa',
                            'question': question,
                            'answer': answer
                        })
        
        return flashcards
    
    def extract_definitions(self, text: str) -> List[Dict[str, str]]:
        """Extract term-definition pairs from text."""
        flashcards = []
        
        for pattern in self.definition_patterns:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                if len(match.groups()) >= 2:
                    term = match.group(1).strip()
                    definition = match.group(2).strip()
                    # Filter out very short or very long terms/definitions
                    if 2 <= len(term) <= 100 and 5 <= len(definition) <= 500:
                        flashcards.append({
                            'type': 'definition',
                            'term': term,
                            'definition': definition
                        })
        
        return flashcards
    
    def extract_bullet_points(self, text: str) -> List[Dict[str, str]]:
        """Extract bullet points that could be flashcards."""
        flashcards = []
        
        # Look for bullet points or numbered lists
        bullet_pattern = r'[•\-\*]\s*(.+?)(?=\n[•\-\*]|\n\n|\n$)'
        numbered_pattern = r'\d+\.\s*(.+?)(?=\n\d+\.|\n\n|\n$)'
        
        for pattern in [bullet_pattern, numbered_pattern]:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                content = match.group(1).strip()
                if 10 <= len(content) <= 200:  # Reasonable length for a flashcard
                    flashcards.append({
                        'type': 'bullet',
                        'content': content
                    })
        
        return flashcards
    
    def extract_flashcards(self, pdf_path: str, method: str = 'pdfplumber', max_pages: int = None, start_page: int = 1, use_ocr: bool = False) -> List[Dict[str, str]]:
        """Main method to extract flashcards from PDF."""
        print(f"从 {pdf_path} 提取闪卡...")

        # Use grid-based extraction if enabled
        if self.grid_mode:
            return self.extract_grid_flashcards(pdf_path, max_pages, start_page, use_ocr)

        # Extract text using traditional method
        if method == 'pypdf2':
            text = self.extract_text_pypdf2(pdf_path)
        else:
            text = self.extract_text_pdfplumber(pdf_path)

        if not text:
            print("未从PDF中提取到文本")
            return []

        # Clean text
        text = self.clean_text(text)

        # Extract different types of flashcards
        flashcards = []
        flashcards.extend(self.extract_qa_pairs(text))
        flashcards.extend(self.extract_definitions(text))
        flashcards.extend(self.extract_bullet_points(text))

        # Remove duplicates
        seen = set()
        unique_flashcards = []
        for card in flashcards:
            card_str = str(sorted(card.items()))
            if card_str not in seen:
                seen.add(card_str)
                unique_flashcards.append(card)

        return unique_flashcards
    
    def save_flashcards(self, flashcards: List[Dict[str, str]], output_path: str, format: str = 'json'):
        """Save flashcards to file."""
        if format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(flashcards, f, indent=2, ensure_ascii=False)
        elif format == 'txt':
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, card in enumerate(flashcards, 1):
                    f.write(f"=== 闪卡 {i} ===\n")
                    if card['type'] == 'qa':
                        f.write(f"问题: {card['question']}\n")
                        f.write(f"答案: {card['answer']}\n")
                    elif card['type'] == 'definition':
                        f.write(f"术语: {card['term']}\n")
                        f.write(f"定义: {card['definition']}\n")
                    elif card['type'] == 'bullet':
                        f.write(f"内容: {card['content']}\n")
                    elif card['type'] == 'vocabulary':
                        f.write(f"单词: {card['word']}\n")
                        f.write(f"发音: {card['pronunciation']}\n")
                        f.write(f"定义: {card['definition']}\n")
                        f.write(f"位置: {card['position']}\n")
                    elif card['type'] == 'word_formation':
                        components_str = ' + '.join(card['components'])
                        f.write(f"构词: {components_str} = {card['result']}\n")
                        f.write(f"位置: {card['position']}\n")
                    elif card['type'] == 'general':
                        f.write(f"内容: {card['content']}\n")
                        f.write(f"位置: {card['position']}\n")
                    f.write("\n")

        print(f"已保存 {len(flashcards)} 张闪卡到 {output_path}")


def main():
    parser = argparse.ArgumentParser(description='从PDF文件中提取闪卡')
    parser.add_argument('pdf_path', help='PDF文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径 (默认: flashcards.json)')
    parser.add_argument('-f', '--format', choices=['json', 'txt'], default='json',
                       help='输出格式 (默认: json)')
    parser.add_argument('-m', '--method', choices=['pypdf2', 'pdfplumber'], default='pdfplumber',
                       help='文本提取方法 (默认: pdfplumber)')
    parser.add_argument('-g', '--grid', action='store_true',
                       help='使用网格模式提取 (适用于3行7列的规整布局)')
    parser.add_argument('--rows', type=int, default=3,
                       help='网格模式下每页的行数 (默认: 3)')
    parser.add_argument('--cols', type=int, default=7,
                       help='网格模式下每页的列数 (默认: 7)')
    parser.add_argument('--max-pages', type=int, default=None,
                       help='限制处理的页数 (用于测试)')
    parser.add_argument('--start-page', type=int, default=1,
                       help='开始处理的页码 (默认: 1)')
    parser.add_argument('--ocr', action='store_true',
                       help='使用OCR处理扫描版PDF (需要安装tesseract)')

    args = parser.parse_args()

    # Validate input file
    if not Path(args.pdf_path).exists():
        print(f"错误: 文件 {args.pdf_path} 不存在")
        sys.exit(1)

    # Set output path
    if args.output:
        output_path = args.output
    else:
        base_name = Path(args.pdf_path).stem
        extension = 'json' if args.format == 'json' else 'txt'
        output_path = f"{base_name}_flashcards.{extension}"

    # Extract flashcards
    extractor = FlashcardExtractor(grid_mode=args.grid, rows=args.rows, cols=args.cols)
    flashcards = extractor.extract_flashcards(
        args.pdf_path,
        args.method,
        getattr(args, 'max_pages', None),
        getattr(args, 'start_page', 1),
        getattr(args, 'ocr', False)
    )

    if flashcards:
        extractor.save_flashcards(flashcards, output_path, args.format)
        print(f"成功提取了 {len(flashcards)} 张闪卡")

        # Print summary by type
        type_counts = {}
        for card in flashcards:
            card_type = card.get('type', 'unknown')
            type_counts[card_type] = type_counts.get(card_type, 0) + 1

        print("闪卡类型统计:")
        for card_type, count in type_counts.items():
            print(f"  {card_type}: {count} 张")
    else:
        print("在PDF中未找到闪卡")


if __name__ == "__main__":
    main()
