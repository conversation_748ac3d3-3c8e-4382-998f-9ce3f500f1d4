#!/usr/bin/env python3
"""
应用最佳的网格配置到主程序
"""

def update_extract_flashcards_config(rows, cols, top_margin, bottom_margin, left_margin, right_margin):
    """更新extract_flashcards.py中的配置"""
    
    print(f"=== 更新网格配置 ===")
    print(f"行数: {rows}")
    print(f"列数: {cols}")
    print(f"边距: 上{top_margin}%, 下{bottom_margin}%, 左{left_margin}%, 右{right_margin}%")
    
    # 读取当前文件
    with open('extract_flashcards.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新默认行列数
    content = content.replace(
        'def __init__(self, grid_mode=False, rows=7, cols=3, auto_detect_margins=True):',
        f'def __init__(self, grid_mode=False, rows={rows}, cols={cols}, auto_detect_margins=True):'
    )
    
    # 更新默认边距
    old_margins = '''        # Default margins optimized for 7x3 flashcard layout
        self.margins = {
            'top': 3,      # 3% of page height
            'bottom': 3,   # 3% of page height  
            'left': 9,     # 9% of page width (increased to avoid cutting right edge)
            'right': 4     # 4% of page width (reduced to give more space)
        }'''
    
    new_margins = f'''        # Default margins optimized for {rows}x{cols} flashcard layout
        self.margins = {{
            'top': {top_margin},      # {top_margin}% of page height
            'bottom': {bottom_margin},   # {bottom_margin}% of page height  
            'left': {left_margin},     # {left_margin}% of page width
            'right': {right_margin}     # {right_margin}% of page width
        }}'''
    
    content = content.replace(old_margins, new_margins)
    
    # 更新命令行参数默认值
    content = content.replace(
        "parser.add_argument('--rows', type=int, default=7,",
        f"parser.add_argument('--rows', type=int, default={rows},"
    )
    content = content.replace(
        "parser.add_argument('--cols', type=int, default=3,",
        f"parser.add_argument('--cols', type=int, default={cols},"
    )
    
    # 更新帮助文本
    content = content.replace(
        "help='网格模式下每页的行数 (默认: 7)')",
        f"help='网格模式下每页的行数 (默认: {rows})')"
    )
    content = content.replace(
        "help='网格模式下每页的列数 (默认: 3)')",
        f"help='网格模式下每页的列数 (默认: {cols})')"
    )
    
    # 写回文件
    with open('extract_flashcards.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("配置已更新到 extract_flashcards.py")

def test_config(rows, cols, top_margin, bottom_margin, left_margin, right_margin):
    """测试指定的配置"""
    
    print(f"\n=== 测试配置 ===")
    print(f"行数: {rows}, 列数: {cols}")
    print(f"边距: 上{top_margin}%, 下{bottom_margin}%, 左{left_margin}%, 右{right_margin}%")
    
    import subprocess
    import sys
    
    # 先更新配置
    update_extract_flashcards_config(rows, cols, top_margin, bottom_margin, left_margin, right_margin)
    
    # 运行测试
    cmd = [
        sys.executable, 'extract_flashcards.py',
        'data/flashcards.pdf',
        '-g', '--ocr', '-f', 'txt',
        '--max-pages', '1',
        '--start-page', '100',
        '-o', f'test_config_{rows}x{cols}.txt'
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        # 设置环境
        import os
        env = os.environ.copy()
        env['PATH'] = '/Users/<USER>/miniforge3/envs/pdfextract/bin:' + env.get('PATH', '')
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=120)
        
        if result.returncode == 0:
            print("测试成功完成!")
            print(f"输出文件: test_config_{rows}x{cols}.txt")
        else:
            print(f"测试失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("测试超时")
    except Exception as e:
        print(f"测试出错: {e}")

def main():
    print("=== 网格配置工具 ===")
    print("请根据生成的调试图像选择最佳配置")
    print()
    
    # 预设一些可能的最佳配置
    suggested_configs = [
        # (name, rows, cols, top%, bottom%, left%, right%)
        ("配置1: 7行标准", 7, 3, 3, 3, 9, 4),
        ("配置2: 6行较大", 6, 3, 5, 5, 9, 4),
        ("配置3: 8行较小", 8, 3, 3, 3, 9, 4),
        ("配置4: 7行调整边距", 7, 3, 5, 3, 9, 4),
        ("配置5: 7行大边距", 7, 3, 6, 4, 9, 4),
    ]
    
    print("建议的配置:")
    for i, (name, rows, cols, top, bottom, left, right) in enumerate(suggested_configs):
        print(f"{i+1}. {name}: {rows}行{cols}列, 边距({top}%,{bottom}%,{left}%,{right}%)")
    
    print("6. 自定义配置")
    print("0. 退出")
    
    try:
        choice = input("\n请选择配置 (0-6): ").strip()
        
        if choice == '0':
            print("退出")
            return
        elif choice == '6':
            # 自定义配置
            rows = int(input("行数: "))
            cols = int(input("列数: "))
            top = float(input("上边距(%): "))
            bottom = float(input("下边距(%): "))
            left = float(input("左边距(%): "))
            right = float(input("右边距(%): "))
        elif choice in ['1', '2', '3', '4', '5']:
            idx = int(choice) - 1
            name, rows, cols, top, bottom, left, right = suggested_configs[idx]
            print(f"选择了: {name}")
        else:
            print("无效选择")
            return
        
        # 询问是否测试
        test_choice = input(f"\n是否测试配置 {rows}行{cols}列? (y/n): ").strip().lower()
        if test_choice == 'y':
            test_config(rows, cols, top, bottom, left, right)
        else:
            update_extract_flashcards_config(rows, cols, top, bottom, left, right)
            print("配置已更新，未运行测试")
            
    except KeyboardInterrupt:
        print("\n用户取消")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
