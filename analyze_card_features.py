#!/usr/bin/env python3
"""
分析单个卡片的特征，包括尺寸、边框、内容区域等
然后基于单个卡片的尺寸计算整个网格布局
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def detect_card_boundaries(pdf_path, page_num):
    """检测页面中卡片的边界，找出单个卡片的尺寸"""
    
    print(f"=== 分析第{page_num}页的卡片特征 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像用于边界检测
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 转换为灰度图像
            gray_image = pil_image.convert('L')
            img_array = np.array(gray_image)
            
            # 保存原始图像
            pil_image.save(f"page{page_num}_for_analysis.png")
            print(f"已保存原始图像: page{page_num}_for_analysis.png")
            
            # 检测黑色边框线条
            print("\n--- 检测黑色边框线条 ---")
            
            # 检测水平线条
            horizontal_lines = detect_horizontal_lines(img_array)
            print(f"检测到 {len(horizontal_lines)} 条主要水平线")
            
            # 检测垂直线条  
            vertical_lines = detect_vertical_lines(img_array)
            print(f"检测到 {len(vertical_lines)} 条主要垂直线")
            
            if len(horizontal_lines) >= 2 and len(vertical_lines) >= 2:
                # 计算卡片尺寸
                card_info = calculate_card_dimensions(
                    horizontal_lines, vertical_lines, 
                    img_width, img_height, page_width, page_height
                )
                
                if card_info:
                    # 绘制检测结果
                    visualize_detection(
                        pil_image, horizontal_lines, vertical_lines, 
                        card_info, page_num
                    )
                    
                    # 基于卡片尺寸计算完整网格
                    grid_layout = calculate_grid_layout(card_info, page_width, page_height)
                    
                    return card_info, grid_layout
            
            print("未能检测到足够的边框线条")
            return None, None
            
    except Exception as e:
        print(f"卡片特征分析失败: {e}")
        return None, None

def detect_horizontal_lines(img_array, min_length=200, thickness_tolerance=5):
    """检测水平线条"""
    lines = []
    height, width = img_array.shape
    
    # 扫描每一行，寻找长的黑色线条
    for y in range(height):
        row = img_array[y, :]
        black_pixels = row < 100  # 黑色像素阈值
        
        # 找连续的黑色区域
        black_regions = []
        start = None
        for x in range(width):
            if black_pixels[x] and start is None:
                start = x
            elif not black_pixels[x] and start is not None:
                if x - start > min_length:
                    black_regions.append((start, x))
                start = None
        
        # 如果这一行有长的黑色线条，记录这一行
        if black_regions:
            lines.append(y)
    
    # 合并相近的线条（考虑线条粗细）
    merged_lines = []
    if lines:
        current_group = [lines[0]]
        for line in lines[1:]:
            if line - current_group[-1] <= thickness_tolerance:
                current_group.append(line)
            else:
                # 取当前组的中位数作为线条位置
                merged_lines.append(int(np.median(current_group)))
                current_group = [line]
        merged_lines.append(int(np.median(current_group)))
    
    return merged_lines

def detect_vertical_lines(img_array, min_length=200, thickness_tolerance=5):
    """检测垂直线条"""
    lines = []
    height, width = img_array.shape
    
    # 扫描每一列，寻找长的黑色线条
    for x in range(width):
        col = img_array[:, x]
        black_pixels = col < 100  # 黑色像素阈值
        
        # 找连续的黑色区域
        black_regions = []
        start = None
        for y in range(height):
            if black_pixels[y] and start is None:
                start = y
            elif not black_pixels[y] and start is not None:
                if y - start > min_length:
                    black_regions.append((start, y))
                start = None
        
        # 如果这一列有长的黑色线条，记录这一列
        if black_regions:
            lines.append(x)
    
    # 合并相近的线条
    merged_lines = []
    if lines:
        current_group = [lines[0]]
        for line in lines[1:]:
            if line - current_group[-1] <= thickness_tolerance:
                current_group.append(line)
            else:
                merged_lines.append(int(np.median(current_group)))
                current_group = [line]
        merged_lines.append(int(np.median(current_group)))
    
    return merged_lines

def calculate_card_dimensions(h_lines, v_lines, img_width, img_height, page_width, page_height):
    """基于检测到的线条计算卡片尺寸"""
    
    print(f"\n--- 计算卡片尺寸 ---")
    print(f"水平线位置: {h_lines}")
    print(f"垂直线位置: {v_lines}")
    
    # 计算卡片间距
    if len(h_lines) >= 2:
        h_spacings = [h_lines[i+1] - h_lines[i] for i in range(len(h_lines)-1)]
        avg_card_height_px = np.median(h_spacings)
        print(f"卡片高度 (像素): {avg_card_height_px:.1f}")
    else:
        print("水平线不足，无法计算卡片高度")
        return None
    
    if len(v_lines) >= 2:
        v_spacings = [v_lines[i+1] - v_lines[i] for i in range(len(v_lines)-1)]
        avg_card_width_px = np.median(v_spacings)
        print(f"卡片宽度 (像素): {avg_card_width_px:.1f}")
    else:
        print("垂直线不足，无法计算卡片宽度")
        return None
    
    # 转换为PDF坐标
    card_width_pts = avg_card_width_px / img_width * page_width
    card_height_pts = avg_card_height_px / img_height * page_height
    
    print(f"卡片尺寸 (PDF坐标): {card_width_pts:.1f} x {card_height_pts:.1f} points")
    
    # 计算网格起始位置
    first_v_line_pts = v_lines[0] / img_width * page_width
    first_h_line_pts = h_lines[0] / img_height * page_height
    
    print(f"第一条垂直线位置: {first_v_line_pts:.1f} points")
    print(f"第一条水平线位置: {first_h_line_pts:.1f} points")
    
    # 推断行列数
    estimated_cols = len(v_lines) - 1
    estimated_rows = len(h_lines) - 1
    
    print(f"推断的网格: {estimated_rows} 行 x {estimated_cols} 列")
    
    return {
        'card_width_pts': card_width_pts,
        'card_height_pts': card_height_pts,
        'card_width_px': avg_card_width_px,
        'card_height_px': avg_card_height_px,
        'grid_start_x_pts': first_v_line_pts,
        'grid_start_y_pts': first_h_line_pts,
        'estimated_rows': estimated_rows,
        'estimated_cols': estimated_cols,
        'h_lines': h_lines,
        'v_lines': v_lines
    }

def calculate_grid_layout(card_info, page_width, page_height):
    """基于卡片信息计算完整的网格布局"""
    
    print(f"\n--- 计算网格布局 ---")
    
    rows = card_info['estimated_rows']
    cols = card_info['estimated_cols']
    card_width = card_info['card_width_pts']
    card_height = card_info['card_height_pts']
    start_x = card_info['grid_start_x_pts']
    start_y = card_info['grid_start_y_pts']
    
    print(f"网格: {rows} 行 x {cols} 列")
    print(f"卡片尺寸: {card_width:.1f} x {card_height:.1f} points")
    print(f"网格起始位置: ({start_x:.1f}, {start_y:.1f})")
    
    # 计算每个卡片的坐标
    grid_cells = []
    for row in range(rows):
        for col in range(cols):
            x0 = start_x + col * card_width
            y0 = start_y + row * card_height
            x1 = x0 + card_width
            y1 = y0 + card_height
            
            grid_cells.append({
                'row': row + 1,
                'col': col + 1,
                'x0': x0,
                'y0': y0,
                'x1': x1,
                'y1': y1
            })
    
    # 计算等效的边距百分比
    left_margin_pct = start_x / page_width * 100
    top_margin_pct = start_y / page_height * 100
    
    grid_end_x = start_x + cols * card_width
    grid_end_y = start_y + rows * card_height
    
    right_margin_pct = (page_width - grid_end_x) / page_width * 100
    bottom_margin_pct = (page_height - grid_end_y) / page_height * 100
    
    print(f"等效边距: 上{top_margin_pct:.1f}%, 下{bottom_margin_pct:.1f}%, "
          f"左{left_margin_pct:.1f}%, 右{right_margin_pct:.1f}%")
    
    return {
        'rows': rows,
        'cols': cols,
        'card_width': card_width,
        'card_height': card_height,
        'start_x': start_x,
        'start_y': start_y,
        'cells': grid_cells,
        'margins': {
            'top': top_margin_pct,
            'bottom': bottom_margin_pct,
            'left': left_margin_pct,
            'right': right_margin_pct
        }
    }

def visualize_detection(pil_image, h_lines, v_lines, card_info, page_num):
    """可视化检测结果"""
    
    print(f"\n--- 生成可视化结果 ---")
    
    # 创建检测结果图像
    result_image = pil_image.copy()
    draw = ImageDraw.Draw(result_image)
    
    # 绘制检测到的线条
    for y in h_lines:
        draw.line([(0, y), (pil_image.width, y)], fill='red', width=3)
    
    for x in v_lines:
        draw.line([(x, 0), (x, pil_image.height)], fill='blue', width=3)
    
    # 标注卡片
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    rows = card_info['estimated_rows']
    cols = card_info['estimated_cols']
    
    for row in range(min(rows, 3)):  # 只标注前3行
        for col in range(cols):
            if row < len(h_lines)-1 and col < len(v_lines)-1:
                x = (v_lines[col] + v_lines[col+1]) // 2
                y = (h_lines[row] + h_lines[row+1]) // 2
                text = f"R{row+1}C{col+1}"
                
                bbox = draw.textbbox((x, y), text, font=font)
                draw.rectangle(bbox, fill='yellow', outline='black')
                draw.text((x, y), text, fill='black', font=font, anchor='mm')
    
    # 保存结果
    result_filename = f"card_detection_page{page_num}.png"
    result_image.save(result_filename)
    print(f"已保存检测结果: {result_filename}")

def extract_sample_cards(pdf_path, page_num, grid_layout):
    """基于检测到的网格提取样本卡片"""
    
    print(f"\n--- 提取样本卡片 ---")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 提取前几个卡片作为样本
            sample_cells = grid_layout['cells'][:6]  # 前6个卡片
            
            for cell in sample_cells:
                row, col = cell['row'], cell['col']
                x0, y0, x1, y1 = cell['x0'], cell['y0'], cell['x1'], cell['y1']
                
                print(f"卡片 R{row}C{col}: ({x0:.1f}, {y0:.1f}) -> ({x1:.1f}, {y1:.1f})")
                
                try:
                    # 裁剪卡片
                    card = page.crop((x0, y0, x1, y1))
                    card_image = card.to_image(resolution=300)
                    card_filename = f"detected_card_r{row}c{col}.png"
                    card_image.save(card_filename)
                    
                    print(f"  已保存: {card_filename}")
                    
                except Exception as e:
                    print(f"  裁剪失败: {e}")
                    
    except Exception as e:
        print(f"样本提取失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    # 分析卡片特征
    card_info, grid_layout = detect_card_boundaries(pdf_path, page_num)
    
    if card_info and grid_layout:
        print(f"\n=== 分析结果 ===")
        print(f"检测到的网格: {grid_layout['rows']} 行 x {grid_layout['cols']} 列")
        print(f"卡片尺寸: {card_info['card_width_pts']:.1f} x {card_info['card_height_pts']:.1f} points")
        print(f"建议的边距配置:")
        margins = grid_layout['margins']
        print(f"  上边距: {margins['top']:.1f}%")
        print(f"  下边距: {margins['bottom']:.1f}%")
        print(f"  左边距: {margins['left']:.1f}%")
        print(f"  右边距: {margins['right']:.1f}%")
        
        # 提取样本卡片
        extract_sample_cards(pdf_path, page_num, grid_layout)
        
        print(f"\n请查看生成的图像文件来验证检测结果的准确性")
        
        return grid_layout
    else:
        print("卡片特征分析失败")
        return None

if __name__ == "__main__":
    main()
