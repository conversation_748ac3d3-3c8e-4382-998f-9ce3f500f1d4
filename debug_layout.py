#!/usr/bin/env python3
"""
Debug page layout to understand the actual grid positioning
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import os

def analyze_page_layout(pdf_path, page_num, rows=3, cols=7):
    """Analyze the actual layout of a page to understand grid positioning."""
    
    print(f"\n=== 分析第{page_num}页的布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # Get page dimensions
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # Convert to image for visual analysis
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # Create a copy for drawing grid lines
            debug_image = pil_image.copy()
            draw = ImageDraw.Draw(debug_image)
            
            # Draw current grid (what we're using now)
            print(f"\n--- 当前网格划分 ({rows}行 x {cols}列) ---")
            
            cell_width = page_width / cols
            cell_height = page_height / rows
            
            print(f"单元格尺寸: {cell_width:.2f} x {cell_height:.2f} points")
            
            # Convert to image coordinates
            img_cell_width = img_width / cols
            img_cell_height = img_height / rows
            
            print(f"图像单元格尺寸: {img_cell_width:.2f} x {img_cell_height:.2f} pixels")
            
            # Draw grid lines
            for i in range(cols + 1):
                x = int(i * img_cell_width)
                draw.line([(x, 0), (x, img_height)], fill='red', width=2)
            
            for i in range(rows + 1):
                y = int(i * img_cell_height)
                draw.line([(0, y), (img_width, y)], fill='red', width=2)
            
            # Add cell numbers
            try:
                # Try to use a font, fallback to default if not available
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            for row in range(rows):
                for col in range(cols):
                    x = int((col + 0.5) * img_cell_width)
                    y = int((row + 0.5) * img_cell_height)
                    text = f"R{row+1}C{col+1}"
                    
                    # Draw text with background
                    bbox = draw.textbbox((x, y), text, font=font)
                    draw.rectangle(bbox, fill='white', outline='black')
                    draw.text((x, y), text, fill='black', font=font, anchor='mm')
            
            # Save debug image
            debug_filename = f"debug_layout_page{page_num}_current.png"
            debug_image.save(debug_filename)
            print(f"已保存当前网格图像: {debug_filename}")
            
            # Now let's try to detect the actual content area
            print(f"\n--- 尝试检测实际内容区域 ---")
            
            # Convert to grayscale and analyze content distribution
            gray_image = pil_image.convert('L')
            
            # Find content boundaries by analyzing pixel intensity
            import numpy as np
            img_array = np.array(gray_image)
            
            # Find non-white areas (assuming white background)
            content_mask = img_array < 240  # Threshold for non-white content
            
            # Find bounding box of content
            content_rows = np.any(content_mask, axis=1)
            content_cols = np.any(content_mask, axis=0)
            
            if np.any(content_rows) and np.any(content_cols):
                top = np.argmax(content_rows)
                bottom = len(content_rows) - np.argmax(content_rows[::-1]) - 1
                left = np.argmax(content_cols)
                right = len(content_cols) - np.argmax(content_cols[::-1]) - 1
                
                print(f"检测到的内容边界:")
                print(f"  顶部: {top} pixels ({top/img_height*100:.1f}%)")
                print(f"  底部: {bottom} pixels ({bottom/img_height*100:.1f}%)")
                print(f"  左侧: {left} pixels ({left/img_width*100:.1f}%)")
                print(f"  右侧: {right} pixels ({right/img_width*100:.1f}%)")
                
                # Calculate content area
                content_width = right - left
                content_height = bottom - top
                
                print(f"内容区域尺寸: {content_width} x {content_height} pixels")
                print(f"内容区域占比: {content_width/img_width*100:.1f}% x {content_height/img_height*100:.1f}%")
                
                # Draw adjusted grid based on content area
                adjusted_image = pil_image.copy()
                draw_adj = ImageDraw.Draw(adjusted_image)
                
                # Draw content boundary
                draw_adj.rectangle([left, top, right, bottom], outline='blue', width=3)
                
                # Draw adjusted grid within content area
                adj_cell_width = content_width / cols
                adj_cell_height = content_height / rows
                
                print(f"调整后单元格尺寸: {adj_cell_width:.2f} x {adj_cell_height:.2f} pixels")
                
                for i in range(cols + 1):
                    x = int(left + i * adj_cell_width)
                    draw_adj.line([(x, top), (x, bottom)], fill='green', width=2)
                
                for i in range(rows + 1):
                    y = int(top + i * adj_cell_height)
                    draw_adj.line([(left, y), (right, y)], fill='green', width=2)
                
                # Add cell numbers for adjusted grid
                for row in range(rows):
                    for col in range(cols):
                        x = int(left + (col + 0.5) * adj_cell_width)
                        y = int(top + (row + 0.5) * adj_cell_height)
                        text = f"R{row+1}C{col+1}"
                        
                        bbox = draw_adj.textbbox((x, y), text, font=font)
                        draw_adj.rectangle(bbox, fill='yellow', outline='black')
                        draw_adj.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # Save adjusted grid image
                adj_filename = f"debug_layout_page{page_num}_adjusted.png"
                adjusted_image.save(adj_filename)
                print(f"已保存调整后网格图像: {adj_filename}")
                
                # Calculate margins in points (for PDF coordinates)
                margin_top = top / img_height * page_height
                margin_bottom = (img_height - bottom) / img_height * page_height
                margin_left = left / img_width * page_width
                margin_right = (img_width - right) / img_width * page_width
                
                print(f"\n--- 建议的页边距设置 (PDF坐标) ---")
                print(f"上边距: {margin_top:.2f} points")
                print(f"下边距: {margin_bottom:.2f} points")
                print(f"左边距: {margin_left:.2f} points")
                print(f"右边距: {margin_right:.2f} points")
                
                return {
                    'page_width': page_width,
                    'page_height': page_height,
                    'content_area': {
                        'left': margin_left,
                        'top': margin_top,
                        'right': page_width - margin_right,
                        'bottom': page_height - margin_bottom
                    },
                    'margins': {
                        'top': margin_top,
                        'bottom': margin_bottom,
                        'left': margin_left,
                        'right': margin_right
                    }
                }
            else:
                print("未能检测到明显的内容区域")
                return None
                
    except Exception as e:
        print(f"布局分析失败: {e}")
        return None

def extract_sample_cells(pdf_path, page_num, layout_info, rows=3, cols=7):
    """Extract sample cells using the detected layout."""
    if not layout_info:
        print("没有布局信息，无法提取样本")
        return
    
    print(f"\n=== 使用检测到的布局提取样本单元格 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            content_area = layout_info['content_area']
            content_width = content_area['right'] - content_area['left']
            content_height = content_area['bottom'] - content_area['top']
            
            cell_width = content_width / cols
            cell_height = content_height / rows
            
            # Extract a few sample cells
            sample_cells = [(0, 1), (0, 2), (1, 1), (1, 2)]  # Row, Col (0-based)
            
            for row, col in sample_cells:
                x0 = content_area['left'] + col * cell_width
                y0 = content_area['top'] + row * cell_height
                x1 = content_area['left'] + (col + 1) * cell_width
                y1 = content_area['top'] + (row + 1) * cell_height
                
                print(f"\n单元格 R{row+1}C{col+1}:")
                print(f"  坐标: ({x0:.2f}, {y0:.2f}) -> ({x1:.2f}, {y1:.2f})")
                print(f"  尺寸: {x1-x0:.2f} x {y1-y0:.2f}")
                
                # Crop and save cell
                cell = page.crop((x0, y0, x1, y1))
                cell_image = cell.to_image(resolution=300)
                cell_filename = f"sample_cell_page{page_num}_r{row+1}_c{col+1}.png"
                cell_image.save(cell_filename)
                print(f"  已保存: {cell_filename}")
                
                # Try to extract text
                cell_text = cell.extract_text()
                print(f"  传统提取: '{cell_text}'")
                
    except Exception as e:
        print(f"样本提取失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100  # Test page
    
    # Analyze layout
    layout_info = analyze_page_layout(pdf_path, page_num)
    
    # Extract sample cells
    extract_sample_cells(pdf_path, page_num, layout_info)
    
    print(f"\n=== 分析完成 ===")
    print("请查看生成的图像文件来验证网格划分是否正确")

if __name__ == "__main__":
    main()
