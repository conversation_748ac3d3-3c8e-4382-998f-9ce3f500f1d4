#!/usr/bin/env python3
"""
应用精确调整后的配置：卡片高度+8%，行间隔-60%
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont

def calculate_precise_layout(page_width, page_height):
    """计算精确调整后的布局参数"""
    
    print("=== 计算精确调整后的布局 ===")
    
    # 基础参数
    rows, cols = 7, 3
    
    # 边距（使用之前确定的最佳值）
    left_margin_pct = 9
    right_margin_pct = 4
    top_margin_pct = 3
    bottom_margin_pct = 3
    
    # 计算边距
    top_margin = page_height * top_margin_pct / 100
    bottom_margin = page_height * bottom_margin_pct / 100
    left_margin = page_width * left_margin_pct / 100
    right_margin = page_width * right_margin_pct / 100
    
    # 可用区域
    available_width = page_width - left_margin - right_margin
    available_height = page_height - top_margin - bottom_margin
    
    # 列配置（已知正确）
    col_gap_ratio = 0.02
    card_width = available_width * (1 - (cols-1) * col_gap_ratio) / cols
    col_gap = available_width * col_gap_ratio
    
    # 原始行配置（从之前最好的配置开始）
    original_card_height_ratio = 0.11  # 假设这是最接近的配置
    original_row_gap_ratio = 0.025
    
    # 应用调整
    # 卡片高度增加8%
    adjusted_card_height_ratio = original_card_height_ratio * 1.08
    
    # 行间隔减少60%
    adjusted_row_gap_ratio = original_row_gap_ratio * 0.4
    
    # 计算调整后的尺寸
    card_height = available_height * adjusted_card_height_ratio
    row_gap = available_height * adjusted_row_gap_ratio
    
    print(f"调整前: 卡片高度比例 {original_card_height_ratio:.3f}, 行间隔比例 {original_row_gap_ratio:.3f}")
    print(f"调整后: 卡片高度比例 {adjusted_card_height_ratio:.3f}, 行间隔比例 {adjusted_row_gap_ratio:.3f}")
    print(f"卡片尺寸: {card_width:.1f} x {card_height:.1f} points")
    print(f"间隔: 行间隔 {row_gap:.1f}, 列间隔 {col_gap:.1f} points")
    
    # 检查总高度
    total_height_needed = rows * card_height + (rows - 1) * row_gap
    print(f"总高度: {total_height_needed:.1f} / {available_height:.1f} points")
    
    if total_height_needed > available_height:
        print("警告: 调整后的布局可能超出可用高度")
        # 自动微调以适应
        scale_factor = available_height / total_height_needed
        card_height *= scale_factor
        row_gap *= scale_factor
        print(f"自动缩放后: 卡片高度 {card_height:.1f}, 行间隔 {row_gap:.1f}")
    
    # 计算每个卡片的位置
    cells = []
    for row in range(rows):
        for col in range(cols):
            x0 = left_margin + col * (card_width + col_gap)
            y0 = top_margin + row * (card_height + row_gap)
            x1 = x0 + card_width
            y1 = y0 + card_height
            
            cells.append({
                'row': row + 1,
                'col': col + 1,
                'x0': x0, 'y0': y0, 'x1': x1, 'y1': y1
            })
    
    return {
        'rows': rows, 'cols': cols,
        'card_width': card_width, 'card_height': card_height,
        'row_gap': row_gap, 'col_gap': col_gap,
        'margins': {
            'top': top_margin_pct, 'bottom': bottom_margin_pct,
            'left': left_margin_pct, 'right': right_margin_pct
        },
        'cells': cells,
        'card_height_ratio': adjusted_card_height_ratio,
        'row_gap_ratio': adjusted_row_gap_ratio
    }

def visualize_precise_layout(pdf_path, page_num, layout):
    """可视化精确调整后的布局"""
    
    print(f"\n=== 可视化精确布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 转换为图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            # 创建可视化
            vis_image = pil_image.copy()
            draw = ImageDraw.Draw(vis_image)
            
            img_width, img_height = pil_image.size
            page_width = page.width
            page_height = page.height
            
            # 转换坐标
            def pdf_to_img(x, y):
                img_x = int(x / page_width * img_width)
                img_y = int(y / page_height * img_height)
                return img_x, img_y
            
            # 绘制所有卡片边界
            for i, cell in enumerate(layout['cells']):
                x0, y0 = pdf_to_img(cell['x0'], cell['y0'])
                x1, y1 = pdf_to_img(cell['x1'], cell['y1'])
                
                # 绘制卡片边框
                draw.rectangle([x0, y0, x1, y1], outline='red', width=3)
                
                # 标注前几个卡片
                if i < 9:  # 标注前3行
                    try:
                        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
                    except:
                        font = ImageFont.load_default()
                    
                    center_x, center_y = (x0 + x1) // 2, (y0 + y1) // 2
                    text = f"R{cell['row']}C{cell['col']}"
                    
                    bbox = draw.textbbox((center_x, center_y), text, font=font)
                    draw.rectangle(bbox, fill='yellow', outline='black')
                    draw.text((center_x, center_y), text, fill='black', font=font, anchor='mm')
            
            # 保存可视化结果
            vis_filename = f"precise_layout_page{page_num}.png"
            vis_image.save(vis_filename)
            print(f"已保存精确布局可视化: {vis_filename}")
            
    except Exception as e:
        print(f"可视化失败: {e}")

def extract_precise_samples(pdf_path, page_num, layout):
    """提取精确调整后的样本卡片"""
    
    print(f"\n=== 提取精确样本卡片 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 提取前6个卡片
            for i in range(min(6, len(layout['cells']))):
                cell = layout['cells'][i]
                
                try:
                    card = page.crop((cell['x0'], cell['y0'], cell['x1'], cell['y1']))
                    card_image = card.to_image(resolution=300)
                    card_filename = f"precise_card_r{cell['row']}c{cell['col']}.png"
                    card_image.save(card_filename)
                    
                    print(f"已保存: {card_filename}")
                    
                except Exception as e:
                    print(f"卡片 R{cell['row']}C{cell['col']} 提取失败: {e}")
                    
    except Exception as e:
        print(f"样本提取失败: {e}")

def update_main_program(layout):
    """更新主程序的配置"""
    
    print(f"\n=== 更新主程序配置 ===")
    
    # 读取当前文件
    try:
        with open('extract_flashcards.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新默认边距为精确值
        old_margins_pattern = r'self\.margins = \{[^}]+\}'
        
        new_margins = f'''self.margins = {{
            'top': {layout['margins']['top']},      # {layout['margins']['top']}% of page height
            'bottom': {layout['margins']['bottom']},   # {layout['margins']['bottom']}% of page height  
            'left': {layout['margins']['left']},     # {layout['margins']['left']}% of page width
            'right': {layout['margins']['right']}     # {layout['margins']['right']}% of page width
        }}'''
        
        import re
        content = re.sub(old_margins_pattern, new_margins, content)
        
        # 写回文件
        with open('extract_flashcards.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("主程序配置已更新")
        
        # 同时保存精确的布局参数到新文件
        with open('precise_layout_config.py', 'w', encoding='utf-8') as f:
            f.write(f"""# 精确的布局配置参数
# 基于用户反馈调整：卡片高度+8%，行间隔-60%

LAYOUT_CONFIG = {{
    'rows': {layout['rows']},
    'cols': {layout['cols']},
    'card_height_ratio': {layout['card_height_ratio']:.6f},
    'row_gap_ratio': {layout['row_gap_ratio']:.6f},
    'col_gap_ratio': 0.02,
    'margins': {{
        'top': {layout['margins']['top']},
        'bottom': {layout['margins']['bottom']},
        'left': {layout['margins']['left']},
        'right': {layout['margins']['right']}
    }}
}}

# 使用说明：
# 卡片高度 = 可用高度 * card_height_ratio
# 行间隔 = 可用高度 * row_gap_ratio
# 列间隔 = 可用宽度 * col_gap_ratio
""")
        
        print("精确配置已保存到 precise_layout_config.py")
        
    except Exception as e:
        print(f"更新配置失败: {e}")

def test_precise_config(pdf_path, page_num):
    """测试精确配置"""
    
    print(f"\n=== 测试精确配置 ===")
    
    import subprocess
    import sys
    import os
    
    try:
        # 运行主程序测试
        cmd = [
            sys.executable, 'extract_flashcards.py',
            pdf_path, '-g', '--ocr', '-f', 'txt',
            '--max-pages', '1', '--start-page', str(page_num),
            '-o', 'test_precise_config.txt'
        ]
        
        env = os.environ.copy()
        env['PATH'] = '/Users/<USER>/miniforge3/envs/pdfextract/bin:' + env.get('PATH', '')
        
        print("运行测试...")
        result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=120)
        
        if result.returncode == 0:
            print("✅ 精确配置测试成功！")
            print("输出文件: test_precise_config.txt")
        else:
            print(f"❌ 测试失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
    except Exception as e:
        print(f"❌ 测试出错: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    # 计算精确布局
    page_width, page_height = 595.22, 842.00  # 标准A4尺寸
    layout = calculate_precise_layout(page_width, page_height)
    
    # 可视化
    visualize_precise_layout(pdf_path, page_num, layout)
    
    # 提取样本
    extract_precise_samples(pdf_path, page_num, layout)
    
    # 更新主程序
    update_main_program(layout)
    
    # 询问是否测试
    test_choice = input("\n是否运行测试验证精确配置? (y/n): ").strip().lower()
    if test_choice == 'y':
        test_precise_config(pdf_path, page_num)
    
    print(f"\n🎉 精确配置应用完成！")
    print("请查看生成的图像和样本卡片来验证效果")

if __name__ == "__main__":
    main()
