#!/usr/bin/env python3
"""
直接分析PDF页面的真实布局，找出正确的网格划分方法
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import os

def save_full_page_with_grids(pdf_path, page_num):
    """保存完整页面，并尝试不同的网格划分方案"""
    
    print(f"=== 分析第{page_num}页的真实布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 保存原始页面
            original_filename = f"page{page_num}_original.png"
            pil_image.save(original_filename)
            print(f"已保存原始页面: {original_filename}")
            
            # 尝试不同的网格方案
            grid_configs = [
                # (name, top_margin%, bottom_margin%, left_margin%, right_margin%)
                ("无边距", 0, 0, 0, 0),
                ("小边距", 5, 5, 5, 5),
                ("中等边距", 10, 10, 10, 10),
                ("大边距", 15, 15, 15, 15),
                ("自定义1", 8, 3, 8, 5),
                ("自定义2", 12, 5, 12, 8),
            ]
            
            for config_name, top_pct, bottom_pct, left_pct, right_pct in grid_configs:
                print(f"\n--- 测试配置: {config_name} ---")
                
                # 创建网格图像
                grid_image = pil_image.copy()
                draw = ImageDraw.Draw(grid_image)
                
                # 计算内容区域
                margin_top = img_height * top_pct / 100
                margin_bottom = img_height * bottom_pct / 100
                margin_left = img_width * left_pct / 100
                margin_right = img_width * right_pct / 100
                
                content_top = margin_top
                content_bottom = img_height - margin_bottom
                content_left = margin_left
                content_right = img_width - margin_right
                
                content_width = content_right - content_left
                content_height = content_bottom - content_top
                
                print(f"内容区域: {content_width:.0f} x {content_height:.0f} pixels")
                
                # 绘制内容区域边界
                draw.rectangle([content_left, content_top, content_right, content_bottom], 
                              outline='blue', width=3)
                
                # 绘制3x7网格
                rows, cols = 3, 7
                cell_width = content_width / cols
                cell_height = content_height / rows
                
                print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f} pixels")
                
                # 绘制网格线
                for i in range(cols + 1):
                    x = int(content_left + i * cell_width)
                    draw.line([(x, content_top), (x, content_bottom)], fill='red', width=2)
                
                for i in range(rows + 1):
                    y = int(content_top + i * cell_height)
                    draw.line([(content_left, y), (content_right, y)], fill='red', width=2)
                
                # 添加单元格标签
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
                except:
                    font = ImageFont.load_default()
                
                for row in range(rows):
                    for col in range(cols):
                        x = int(content_left + (col + 0.5) * cell_width)
                        y = int(content_top + (row + 0.5) * cell_height)
                        text = f"R{row+1}C{col+1}"
                        
                        # 绘制标签背景
                        bbox = draw.textbbox((x, y), text, font=font)
                        draw.rectangle(bbox, fill='yellow', outline='black')
                        draw.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # 保存网格图像
                grid_filename = f"page{page_num}_grid_{config_name.replace(' ', '_')}.png"
                grid_image.save(grid_filename)
                print(f"已保存网格图像: {grid_filename}")
                
                # 提取几个样本单元格
                sample_positions = [(0, 1), (1, 2)]  # R1C2, R2C3
                
                for row, col in sample_positions:
                    # 计算PDF坐标
                    pdf_margin_top = page_height * top_pct / 100
                    pdf_margin_left = page_width * left_pct / 100
                    pdf_content_width = page_width * (100 - left_pct - right_pct) / 100
                    pdf_content_height = page_height * (100 - top_pct - bottom_pct) / 100
                    
                    pdf_cell_width = pdf_content_width / cols
                    pdf_cell_height = pdf_content_height / rows
                    
                    x0 = pdf_margin_left + col * pdf_cell_width
                    y0 = pdf_margin_top + row * pdf_cell_height
                    x1 = pdf_margin_left + (col + 1) * pdf_cell_width
                    y1 = pdf_margin_top + (row + 1) * pdf_cell_height
                    
                    print(f"  单元格 R{row+1}C{col+1}: ({x0:.1f}, {y0:.1f}) -> ({x1:.1f}, {y1:.1f})")
                    
                    try:
                        # 裁剪单元格
                        cell = page.crop((x0, y0, x1, y1))
                        cell_image = cell.to_image(resolution=300)
                        cell_filename = f"cell_{config_name.replace(' ', '_')}_r{row+1}c{col+1}.png"
                        cell_image.save(cell_filename)
                        print(f"    已保存: {cell_filename}")
                        
                    except Exception as e:
                        print(f"    裁剪失败: {e}")
            
            print(f"\n=== 分析完成 ===")
            print("请查看生成的图像文件，找出最合适的网格配置")
            print("特别注意哪个配置能完整包含闪卡内容而不被截断")
            
    except Exception as e:
        print(f"分析失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    save_full_page_with_grids(pdf_path, page_num)

if __name__ == "__main__":
    main()
