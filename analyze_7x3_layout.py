#!/usr/bin/env python3
"""
分析7行3列的闪卡布局，每个卡片有黑色边框
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def analyze_7x3_layout(pdf_path, page_num):
    """分析7x3布局的闪卡页面"""
    
    print(f"=== 分析第{page_num}页的7行3列布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 保存原始页面
            original_filename = f"page{page_num}_7x3_original.png"
            pil_image.save(original_filename)
            print(f"已保存原始页面: {original_filename}")
            
            # 尝试不同的7x3网格配置
            grid_configs = [
                # (name, top_margin%, bottom_margin%, left_margin%, right_margin%)
                ("7x3_无边距", 0, 0, 0, 0),
                ("7x3_小边距", 3, 3, 5, 5),
                ("7x3_中等边距", 5, 5, 8, 8),
                ("7x3_大边距", 8, 8, 12, 12),
                ("7x3_自定义1", 4, 2, 10, 10),
                ("7x3_自定义2", 6, 4, 12, 8),
            ]
            
            for config_name, top_pct, bottom_pct, left_pct, right_pct in grid_configs:
                print(f"\n--- 测试7x3配置: {config_name} ---")
                
                # 创建网格图像
                grid_image = pil_image.copy()
                draw = ImageDraw.Draw(grid_image)
                
                # 计算内容区域
                margin_top = img_height * top_pct / 100
                margin_bottom = img_height * bottom_pct / 100
                margin_left = img_width * left_pct / 100
                margin_right = img_width * right_pct / 100
                
                content_top = margin_top
                content_bottom = img_height - margin_bottom
                content_left = margin_left
                content_right = img_width - margin_right
                
                content_width = content_right - content_left
                content_height = content_bottom - content_top
                
                print(f"内容区域: {content_width:.0f} x {content_height:.0f} pixels")
                
                # 绘制内容区域边界
                draw.rectangle([content_left, content_top, content_right, content_bottom], 
                              outline='blue', width=4)
                
                # 绘制7x3网格
                rows, cols = 7, 3
                cell_width = content_width / cols
                cell_height = content_height / rows
                
                print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f} pixels")
                print(f"PDF单元格尺寸: {cell_width/img_width*page_width:.1f} x {cell_height/img_height*page_height:.1f} points")
                
                # 绘制网格线
                for i in range(cols + 1):
                    x = int(content_left + i * cell_width)
                    draw.line([(x, content_top), (x, content_bottom)], fill='red', width=3)
                
                for i in range(rows + 1):
                    y = int(content_top + i * cell_height)
                    draw.line([(content_left, y), (content_right, y)], fill='red', width=3)
                
                # 添加单元格标签
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
                except:
                    font = ImageFont.load_default()
                
                for row in range(rows):
                    for col in range(cols):
                        x = int(content_left + (col + 0.5) * cell_width)
                        y = int(content_top + (row + 0.5) * cell_height)
                        text = f"R{row+1}C{col+1}"
                        
                        # 绘制标签背景
                        bbox = draw.textbbox((x, y), text, font=font)
                        draw.rectangle(bbox, fill='yellow', outline='black', width=1)
                        draw.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # 保存网格图像
                grid_filename = f"page{page_num}_grid_{config_name}.png"
                grid_image.save(grid_filename)
                print(f"已保存网格图像: {grid_filename}")
                
                # 提取几个样本单元格
                sample_cells = [(0, 0), (0, 1), (0, 2), (1, 0), (1, 1), (1, 2), (2, 0), (2, 1), (2, 2)]
                
                for row, col in sample_cells:
                    # 计算PDF坐标
                    pdf_margin_top = page_height * top_pct / 100
                    pdf_margin_left = page_width * left_pct / 100
                    pdf_content_width = page_width * (100 - left_pct - right_pct) / 100
                    pdf_content_height = page_height * (100 - top_pct - bottom_pct) / 100
                    
                    pdf_cell_width = pdf_content_width / cols
                    pdf_cell_height = pdf_content_height / rows
                    
                    x0 = pdf_margin_left + col * pdf_cell_width
                    y0 = pdf_margin_top + row * pdf_cell_height
                    x1 = pdf_margin_left + (col + 1) * pdf_cell_width
                    y1 = pdf_margin_top + (row + 1) * pdf_cell_height
                    
                    try:
                        # 裁剪单元格
                        cell = page.crop((x0, y0, x1, y1))
                        cell_image = cell.to_image(resolution=300)
                        cell_filename = f"cell_{config_name}_r{row+1}c{col+1}.png"
                        cell_image.save(cell_filename)
                        
                        # 尝试文本提取
                        cell_text = cell.extract_text()
                        if cell_text and len(cell_text.strip()) > 0:
                            print(f"  R{row+1}C{col+1}: 有文本 (长度: {len(cell_text.strip())})")
                        else:
                            print(f"  R{row+1}C{col+1}: 无文本")
                            
                    except Exception as e:
                        print(f"  R{row+1}C{col+1}: 裁剪失败 - {e}")
            
            print(f"\n=== 7x3布局分析完成 ===")
            print("请查看生成的图像文件，确认哪个配置最准确地对齐了卡片边框")
            print("特别注意网格线是否与卡片的黑色边框对齐")
            
    except Exception as e:
        print(f"分析失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    # 分析7x3布局
    analyze_7x3_layout(pdf_path, page_num)

if __name__ == "__main__":
    main()
