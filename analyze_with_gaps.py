#!/usr/bin/env python3
"""
分析考虑间隔的卡片布局
重点：行与行之间、列与列之间都有小间隔
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def analyze_card_layout_with_gaps(pdf_path, page_num):
    """分析考虑间隔的卡片布局"""
    
    print(f"=== 分析第{page_num}页的卡片布局（考虑间隔） ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 保存原始图像用于参考
            pil_image.save(f"page{page_num}_gap_analysis.png")
            
            # 基于已知的纵向配置（左9%，右4%），我们知道列间隔是正确的
            # 现在重点分析行间隔
            
            # 假设的配置参数
            test_configs = [
                # (name, rows, cols, card_height_ratio, row_gap_ratio, top_margin%, bottom_margin%)
                ("标准7行_小间隔", 7, 3, 0.12, 0.015, 3, 3),
                ("标准7行_中间隔", 7, 3, 0.11, 0.025, 3, 3),
                ("标准7行_大间隔", 7, 3, 0.10, 0.035, 3, 3),
                ("6行_小间隔", 6, 3, 0.14, 0.02, 3, 3),
                ("6行_中间隔", 6, 3, 0.13, 0.03, 3, 3),
                ("8行_小间隔", 8, 3, 0.10, 0.015, 3, 3),
                ("8行_中间隔", 8, 3, 0.09, 0.025, 3, 3),
                ("7行_调整边距1", 7, 3, 0.11, 0.025, 5, 3),
                ("7行_调整边距2", 7, 3, 0.11, 0.025, 4, 2),
                ("7行_调整边距3", 7, 3, 0.11, 0.025, 6, 4),
            ]
            
            for config_name, rows, cols, card_h_ratio, gap_h_ratio, top_margin_pct, bottom_margin_pct in test_configs:
                print(f"\n--- 测试配置: {config_name} ---")
                
                # 计算布局参数
                layout = calculate_layout_with_gaps(
                    page_width, page_height, rows, cols,
                    card_h_ratio, gap_h_ratio, 
                    top_margin_pct, bottom_margin_pct
                )
                
                if layout:
                    # 可视化这个配置
                    visualize_layout_with_gaps(pil_image, layout, config_name, page_num)
                    
                    # 提取样本卡片
                    extract_sample_with_gaps(pdf_path, page_num, layout, config_name)
            
            print(f"\n=== 分析完成 ===")
            print("请查看生成的图像，找出间隔最准确的配置")
            
    except Exception as e:
        print(f"间隔分析失败: {e}")

def calculate_layout_with_gaps(page_width, page_height, rows, cols, 
                              card_height_ratio, row_gap_ratio, 
                              top_margin_pct, bottom_margin_pct):
    """计算考虑间隔的布局"""
    
    # 使用已知正确的列配置
    left_margin_pct = 9
    right_margin_pct = 4
    col_gap_ratio = 0.02  # 假设列间隔为页面宽度的2%
    
    # 计算边距
    top_margin = page_height * top_margin_pct / 100
    bottom_margin = page_height * bottom_margin_pct / 100
    left_margin = page_width * left_margin_pct / 100
    right_margin = page_width * right_margin_pct / 100
    
    # 可用区域
    available_width = page_width - left_margin - right_margin
    available_height = page_height - top_margin - bottom_margin
    
    # 计算卡片和间隔尺寸
    card_width = available_width * (1 - (cols-1) * col_gap_ratio) / cols
    col_gap = available_width * col_gap_ratio
    
    card_height = available_height * card_height_ratio
    row_gap = available_height * row_gap_ratio
    
    # 检查布局是否合理
    total_height_needed = rows * card_height + (rows - 1) * row_gap
    if total_height_needed > available_height:
        print(f"  警告: 布局超出可用高度 ({total_height_needed:.1f} > {available_height:.1f})")
        return None
    
    print(f"  卡片尺寸: {card_width:.1f} x {card_height:.1f} points")
    print(f"  间隔: 行间隔 {row_gap:.1f}, 列间隔 {col_gap:.1f} points")
    print(f"  总高度: {total_height_needed:.1f} / {available_height:.1f} points")
    
    # 计算每个卡片的位置
    cells = []
    for row in range(rows):
        for col in range(cols):
            x0 = left_margin + col * (card_width + col_gap)
            y0 = top_margin + row * (card_height + row_gap)
            x1 = x0 + card_width
            y1 = y0 + card_height
            
            cells.append({
                'row': row + 1,
                'col': col + 1,
                'x0': x0, 'y0': y0, 'x1': x1, 'y1': y1
            })
    
    return {
        'rows': rows, 'cols': cols,
        'card_width': card_width, 'card_height': card_height,
        'row_gap': row_gap, 'col_gap': col_gap,
        'margins': {
            'top': top_margin, 'bottom': bottom_margin,
            'left': left_margin, 'right': right_margin
        },
        'cells': cells
    }

def visualize_layout_with_gaps(pil_image, layout, config_name, page_num):
    """可视化考虑间隔的布局"""
    
    # 创建可视化图像
    vis_image = pil_image.copy()
    draw = ImageDraw.Draw(vis_image)
    
    img_width, img_height = pil_image.size
    page_width = 595.22  # 假设的页面宽度
    page_height = 842.00  # 假设的页面高度
    
    # 转换坐标到图像坐标系
    def pdf_to_img(x, y):
        img_x = int(x / page_width * img_width)
        img_y = int(y / page_height * img_height)
        return img_x, img_y
    
    # 绘制卡片边界
    for i, cell in enumerate(layout['cells']):
        x0, y0 = pdf_to_img(cell['x0'], cell['y0'])
        x1, y1 = pdf_to_img(cell['x1'], cell['y1'])
        
        # 绘制卡片边框
        draw.rectangle([x0, y0, x1, y1], outline='red', width=2)
        
        # 标注前几个卡片
        if i < 6:
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            center_x, center_y = (x0 + x1) // 2, (y0 + y1) // 2
            text = f"R{cell['row']}C{cell['col']}"
            
            bbox = draw.textbbox((center_x, center_y), text, font=font)
            draw.rectangle(bbox, fill='yellow', outline='black')
            draw.text((center_x, center_y), text, fill='black', font=font, anchor='mm')
    
    # 绘制间隔区域（用不同颜色标示）
    # 行间隔
    for row in range(layout['rows'] - 1):
        cell_bottom = layout['cells'][row * layout['cols']]['y1']
        cell_top = layout['cells'][(row + 1) * layout['cols']]['y0']
        
        x0, y0 = pdf_to_img(layout['margins']['left'], cell_bottom)
        x1, y1 = pdf_to_img(layout['margins']['left'] + layout['card_width'] * layout['cols'] + layout['col_gap'] * (layout['cols']-1), cell_top)
        
        draw.rectangle([x0, y0, x1, y1], fill='lightblue', outline='blue', width=1)
    
    # 保存可视化结果
    vis_filename = f"gap_layout_{config_name}.png"
    vis_image.save(vis_filename)
    print(f"  已保存可视化: {vis_filename}")

def extract_sample_with_gaps(pdf_path, page_num, layout, config_name):
    """提取样本卡片"""
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 提取前3个卡片
            for i in range(min(3, len(layout['cells']))):
                cell = layout['cells'][i]
                
                try:
                    card = page.crop((cell['x0'], cell['y0'], cell['x1'], cell['y1']))
                    card_image = card.to_image(resolution=300)
                    card_filename = f"gap_card_{config_name}_r{cell['row']}c{cell['col']}.png"
                    card_image.save(card_filename)
                    
                    print(f"  已保存卡片: {card_filename}")
                    
                except Exception as e:
                    print(f"  卡片提取失败: {e}")
                    
    except Exception as e:
        print(f"样本提取失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    analyze_card_layout_with_gaps(pdf_path, page_num)

if __name__ == "__main__":
    main()
