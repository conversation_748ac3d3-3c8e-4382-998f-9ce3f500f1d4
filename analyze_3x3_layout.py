#!/usr/bin/env python3
"""
分析3行3列的闪卡布局，每个卡片有黑色边框
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def analyze_3x3_layout(pdf_path, page_num):
    """分析3x3布局的闪卡页面"""
    
    print(f"=== 分析第{page_num}页的3x3布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 保存原始页面
            original_filename = f"page{page_num}_3x3_original.png"
            pil_image.save(original_filename)
            print(f"已保存原始页面: {original_filename}")
            
            # 尝试检测黑色边框来确定卡片位置
            gray_image = pil_image.convert('L')
            img_array = np.array(gray_image)
            
            # 检测黑色线条（边框）
            # 黑色像素通常值很低
            black_threshold = 50
            black_pixels = img_array < black_threshold
            
            print(f"检测到 {np.sum(black_pixels)} 个黑色像素")
            
            # 尝试不同的3x3网格配置
            grid_configs = [
                # (name, top_margin%, bottom_margin%, left_margin%, right_margin%)
                ("3x3_无边距", 0, 0, 0, 0),
                ("3x3_小边距", 5, 5, 5, 5),
                ("3x3_中等边距", 10, 10, 10, 10),
                ("3x3_大边距", 15, 15, 15, 15),
                ("3x3_自定义1", 8, 5, 12, 12),
                ("3x3_自定义2", 12, 8, 15, 15),
            ]
            
            for config_name, top_pct, bottom_pct, left_pct, right_pct in grid_configs:
                print(f"\n--- 测试3x3配置: {config_name} ---")
                
                # 创建网格图像
                grid_image = pil_image.copy()
                draw = ImageDraw.Draw(grid_image)
                
                # 计算内容区域
                margin_top = img_height * top_pct / 100
                margin_bottom = img_height * bottom_pct / 100
                margin_left = img_width * left_pct / 100
                margin_right = img_width * right_pct / 100
                
                content_top = margin_top
                content_bottom = img_height - margin_bottom
                content_left = margin_left
                content_right = img_width - margin_right
                
                content_width = content_right - content_left
                content_height = content_bottom - content_top
                
                print(f"内容区域: {content_width:.0f} x {content_height:.0f} pixels")
                
                # 绘制内容区域边界
                draw.rectangle([content_left, content_top, content_right, content_bottom], 
                              outline='blue', width=4)
                
                # 绘制3x3网格
                rows, cols = 3, 3
                cell_width = content_width / cols
                cell_height = content_height / rows
                
                print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f} pixels")
                print(f"PDF单元格尺寸: {cell_width/img_width*page_width:.1f} x {cell_height/img_height*page_height:.1f} points")
                
                # 绘制网格线
                for i in range(cols + 1):
                    x = int(content_left + i * cell_width)
                    draw.line([(x, content_top), (x, content_bottom)], fill='red', width=3)
                
                for i in range(rows + 1):
                    y = int(content_top + i * cell_height)
                    draw.line([(content_left, y), (content_right, y)], fill='red', width=3)
                
                # 添加单元格标签
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
                except:
                    font = ImageFont.load_default()
                
                for row in range(rows):
                    for col in range(cols):
                        x = int(content_left + (col + 0.5) * cell_width)
                        y = int(content_top + (row + 0.5) * cell_height)
                        text = f"R{row+1}C{col+1}"
                        
                        # 绘制标签背景
                        bbox = draw.textbbox((x, y), text, font=font)
                        draw.rectangle(bbox, fill='yellow', outline='black', width=2)
                        draw.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # 保存网格图像
                grid_filename = f"page{page_num}_grid_{config_name}.png"
                grid_image.save(grid_filename)
                print(f"已保存网格图像: {grid_filename}")
                
                # 提取所有9个单元格作为样本
                for row in range(rows):
                    for col in range(cols):
                        # 计算PDF坐标
                        pdf_margin_top = page_height * top_pct / 100
                        pdf_margin_left = page_width * left_pct / 100
                        pdf_content_width = page_width * (100 - left_pct - right_pct) / 100
                        pdf_content_height = page_height * (100 - top_pct - bottom_pct) / 100
                        
                        pdf_cell_width = pdf_content_width / cols
                        pdf_cell_height = pdf_content_height / rows
                        
                        x0 = pdf_margin_left + col * pdf_cell_width
                        y0 = pdf_margin_top + row * pdf_cell_height
                        x1 = pdf_margin_left + (col + 1) * pdf_cell_width
                        y1 = pdf_margin_top + (row + 1) * pdf_cell_height
                        
                        try:
                            # 裁剪单元格
                            cell = page.crop((x0, y0, x1, y1))
                            cell_image = cell.to_image(resolution=300)
                            cell_filename = f"cell_{config_name}_r{row+1}c{col+1}.png"
                            cell_image.save(cell_filename)
                            
                            # 尝试文本提取
                            cell_text = cell.extract_text()
                            if cell_text and len(cell_text.strip()) > 0:
                                print(f"  R{row+1}C{col+1}: 有文本内容 (长度: {len(cell_text.strip())})")
                            else:
                                print(f"  R{row+1}C{col+1}: 无文本内容")
                                
                        except Exception as e:
                            print(f"  R{row+1}C{col+1}: 裁剪失败 - {e}")
            
            print(f"\n=== 3x3布局分析完成 ===")
            print("请查看生成的图像文件，确认哪个配置最准确地对齐了卡片边框")
            
    except Exception as e:
        print(f"分析失败: {e}")

def detect_card_borders(pdf_path, page_num):
    """尝试自动检测卡片的黑色边框"""
    print(f"\n=== 尝试自动检测卡片边框 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 转换为图像
            page_image = page.to_image(resolution=200)  # 较低分辨率用于边框检测
            pil_image = page_image.original
            
            # 转换为灰度
            gray_image = pil_image.convert('L')
            img_array = np.array(gray_image)
            
            # 检测水平和垂直线条
            # 使用形态学操作检测线条
            from scipy import ndimage
            
            # 检测水平线
            horizontal_kernel = np.ones((1, 50))  # 水平线检测核
            horizontal_lines = ndimage.binary_erosion(img_array < 100, horizontal_kernel)
            
            # 检测垂直线
            vertical_kernel = np.ones((50, 1))  # 垂直线检测核
            vertical_lines = ndimage.binary_erosion(img_array < 100, vertical_kernel)
            
            # 找到线条的位置
            h_positions = np.where(np.any(horizontal_lines, axis=1))[0]
            v_positions = np.where(np.any(vertical_lines, axis=0))[0]
            
            print(f"检测到水平线位置: {len(h_positions)} 条")
            print(f"检测到垂直线位置: {len(v_positions)} 条")
            
            if len(h_positions) >= 4 and len(v_positions) >= 4:  # 3x3网格需要4条线
                # 选择主要的线条位置
                h_lines = []
                v_lines = []
                
                # 聚类相近的线条
                def cluster_lines(positions, min_distance=20):
                    if len(positions) == 0:
                        return []
                    
                    clusters = []
                    current_cluster = [positions[0]]
                    
                    for pos in positions[1:]:
                        if pos - current_cluster[-1] < min_distance:
                            current_cluster.append(pos)
                        else:
                            clusters.append(int(np.mean(current_cluster)))
                            current_cluster = [pos]
                    
                    clusters.append(int(np.mean(current_cluster)))
                    return clusters
                
                h_lines = cluster_lines(h_positions)
                v_lines = cluster_lines(v_positions)
                
                print(f"聚类后水平线: {h_lines}")
                print(f"聚类后垂直线: {v_lines}")
                
                # 如果检测到合理数量的线条，计算网格
                if len(h_lines) >= 2 and len(v_lines) >= 2:
                    # 转换回PDF坐标
                    img_height, img_width = img_array.shape
                    page_width = page.width
                    page_height = page.height
                    
                    pdf_h_lines = [y / img_height * page_height for y in h_lines]
                    pdf_v_lines = [x / img_width * page_width for x in v_lines]
                    
                    print(f"PDF水平线位置: {[f'{y:.1f}' for y in pdf_h_lines]}")
                    print(f"PDF垂直线位置: {[f'{x:.1f}' for x in pdf_v_lines]}")
                    
                    return pdf_h_lines, pdf_v_lines
            
            print("未能检测到足够的边框线条")
            return None, None
            
    except Exception as e:
        print(f"边框检测失败: {e}")
        return None, None

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    # 分析3x3布局
    analyze_3x3_layout(pdf_path, page_num)
    
    # 尝试自动检测边框
    h_lines, v_lines = detect_card_borders(pdf_path, page_num)

if __name__ == "__main__":
    main()
