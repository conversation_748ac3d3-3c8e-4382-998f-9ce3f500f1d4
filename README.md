# PDF闪卡提取器

这是一个用于从PDF文件中提取闪卡内容的Python工具，特别适用于处理规整的网格布局（如3行7列）的学习材料。

## 功能特点

- **网格模式提取**: 专门针对规整布局的PDF（如3行7列的闪卡页面）
- **多种内容识别**: 支持词汇卡、构词法、问答对等多种格式
- **位置信息**: 记录每张闪卡在PDF中的精确位置
- **多种输出格式**: 支持JSON和文本格式输出
- **灵活配置**: 可自定义网格尺寸和提取方法

## 安装依赖

### 方法1: 使用pip安装
```bash
pip install -r requirements.txt
```

### 方法2: 逐个安装核心依赖
```bash
# 核心PDF处理库
pip install PyPDF2 pdfplumber

# 可选：图像处理和OCR功能
pip install Pillow pytesseract

# 可选：数据处理
pip install pandas
```

### 系统依赖 (仅在使用OCR功能时需要)

#### macOS:
```bash
brew install tesseract
```

#### Ubuntu/Debian:
```bash
sudo apt-get install tesseract-ocr
```

#### Windows:
下载并安装 Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki

## 使用方法

### 基本用法（网格模式）
```bash
# 提取3行7列布局的PDF闪卡
python extract_flashcards.py your_pdf.pdf -g

# 输出为文本格式
python extract_flashcards.py your_pdf.pdf -g -f txt

# 自定义输出文件名
python extract_flashcards.py your_pdf.pdf -g -o my_flashcards.json
```

### 高级选项
```bash
# 自定义网格尺寸
python extract_flashcards.py your_pdf.pdf -g --rows 4 --cols 6

# 使用传统文本提取模式
python extract_flashcards.py your_pdf.pdf -m pdfplumber

# 查看所有选项
python extract_flashcards.py --help
```

## 输出格式

### JSON格式示例
```json
[
  {
    "type": "vocabulary",
    "word": "ashamed",
    "pronunciation": "ə'ʃeɪmd",
    "definition": "感到羞耻的",
    "page": 1,
    "position": "Row 1, Col 1",
    "raw_text": "ashamed[ə'ʃeɪmd] 感到羞耻的"
  },
  {
    "type": "word_formation",
    "components": ["ash", "ame", "d"],
    "result": "ashamed",
    "page": 1,
    "position": "Row 1, Col 1"
  }
]
```

## 支持的闪卡类型

1. **词汇卡**: `word[pronunciation]` 格式
2. **构词法**: `word + part + part = result` 格式
3. **问答对**: `Q: ... A: ...` 格式
4. **术语定义**: `Term: Definition` 格式
5. **通用内容**: 其他类型的学习内容

## 故障排除

### 常见问题

1. **"PyPDF2 not found"错误**
   ```bash
   pip install PyPDF2
   ```

2. **"pdfplumber not found"错误**
   ```bash
   pip install pdfplumber
   ```

3. **OCR功能不可用**
   - 确保安装了Tesseract OCR系统依赖
   - 安装Python包: `pip install Pillow pytesseract`

4. **提取结果为空**
   - 检查PDF是否为扫描版本（可能需要OCR）
   - 尝试不同的提取方法: `-m pypdf2` 或 `-m pdfplumber`
   - 确认使用了正确的网格模式: `-g`

## 项目结构

```
pdfextract/
├── extract_flashcards.py  # 主程序
├── requirements.txt       # 依赖列表
└── README.md             # 说明文档
```

## 贡献

欢迎提交问题报告和功能建议！
