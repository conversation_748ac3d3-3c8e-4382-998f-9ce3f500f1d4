#!/usr/bin/env python3
"""
精细调整页边距，基于小边距配置进行微调
"""

import pdfplumber
from PIL import Image, ImageDraw, ImageFont

def fine_tune_7x3_layout(pdf_path, page_num):
    """精细调整7x3布局的页边距"""
    
    print(f"=== 精细调整第{page_num}页的7x3布局 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # 获取页面信息
            page_width = page.width
            page_height = page.height
            print(f"页面尺寸: {page_width:.2f} x {page_height:.2f} points")
            
            # 转换为高分辨率图像
            page_image = page.to_image(resolution=300)
            pil_image = page_image.original
            
            img_width, img_height = pil_image.size
            print(f"图像尺寸: {img_width} x {img_height} pixels")
            
            # 基于小边距配置进行精细调整
            # 小边距是: top=3%, bottom=3%, left=5%, right=5%
            # 现在我们需要增加左边距，减少右边距来向右移动
            
            fine_tune_configs = [
                # (name, top%, bottom%, left%, right%)
                ("原小边距", 3, 3, 5, 5),
                ("增加左边距1", 3, 3, 7, 5),
                ("增加左边距2", 3, 3, 8, 5),
                ("增加左边距3", 3, 3, 9, 5),
                ("增加左边距4", 3, 3, 10, 5),
                ("左边距+减右边距1", 3, 3, 8, 3),
                ("左边距+减右边距2", 3, 3, 9, 3),
                ("左边距+减右边距3", 3, 3, 10, 3),
                ("左边距+减右边距4", 3, 3, 11, 3),
                ("最佳猜测1", 3, 3, 8.5, 4),
                ("最佳猜测2", 3, 3, 9.5, 4),
            ]
            
            for config_name, top_pct, bottom_pct, left_pct, right_pct in fine_tune_configs:
                print(f"\n--- 测试配置: {config_name} ---")
                print(f"边距: 上{top_pct}%, 下{bottom_pct}%, 左{left_pct}%, 右{right_pct}%")
                
                # 创建网格图像
                grid_image = pil_image.copy()
                draw = ImageDraw.Draw(grid_image)
                
                # 计算内容区域
                margin_top = img_height * top_pct / 100
                margin_bottom = img_height * bottom_pct / 100
                margin_left = img_width * left_pct / 100
                margin_right = img_width * right_pct / 100
                
                content_top = margin_top
                content_bottom = img_height - margin_bottom
                content_left = margin_left
                content_right = img_width - margin_right
                
                content_width = content_right - content_left
                content_height = content_bottom - content_top
                
                print(f"内容区域: {content_width:.0f} x {content_height:.0f} pixels")
                
                # 绘制内容区域边界
                draw.rectangle([content_left, content_top, content_right, content_bottom], 
                              outline='blue', width=4)
                
                # 绘制7x3网格
                rows, cols = 7, 3
                cell_width = content_width / cols
                cell_height = content_height / rows
                
                print(f"单元格尺寸: {cell_width:.1f} x {cell_height:.1f} pixels")
                
                # 绘制网格线
                for i in range(cols + 1):
                    x = int(content_left + i * cell_width)
                    draw.line([(x, content_top), (x, content_bottom)], fill='red', width=3)
                
                for i in range(rows + 1):
                    y = int(content_top + i * cell_height)
                    draw.line([(content_left, y), (content_right, y)], fill='red', width=3)
                
                # 添加单元格标签（只标记前3行）
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
                except:
                    font = ImageFont.load_default()
                
                for row in range(min(3, rows)):  # 只标记前3行
                    for col in range(cols):
                        x = int(content_left + (col + 0.5) * cell_width)
                        y = int(content_top + (row + 0.5) * cell_height)
                        text = f"R{row+1}C{col+1}"
                        
                        # 绘制标签背景
                        bbox = draw.textbbox((x, y), text, font=font)
                        draw.rectangle(bbox, fill='yellow', outline='black', width=1)
                        draw.text((x, y), text, fill='black', font=font, anchor='mm')
                
                # 保存网格图像
                grid_filename = f"fine_tune_{config_name.replace(' ', '_').replace('+', '_')}.png"
                grid_image.save(grid_filename)
                print(f"已保存: {grid_filename}")
                
                # 提取第一行的3个单元格作为样本
                for col in range(3):
                    row = 0  # 第一行
                    
                    # 计算PDF坐标
                    pdf_margin_top = page_height * top_pct / 100
                    pdf_margin_left = page_width * left_pct / 100
                    pdf_content_width = page_width * (100 - left_pct - right_pct) / 100
                    pdf_content_height = page_height * (100 - top_pct - bottom_pct) / 100
                    
                    pdf_cell_width = pdf_content_width / cols
                    pdf_cell_height = pdf_content_height / rows
                    
                    x0 = pdf_margin_left + col * pdf_cell_width
                    y0 = pdf_margin_top + row * pdf_cell_height
                    x1 = pdf_margin_left + (col + 1) * pdf_cell_width
                    y1 = pdf_margin_top + (row + 1) * pdf_cell_height
                    
                    try:
                        # 裁剪单元格
                        cell = page.crop((x0, y0, x1, y1))
                        cell_image = cell.to_image(resolution=300)
                        cell_filename = f"cell_{config_name.replace(' ', '_').replace('+', '_')}_r1c{col+1}.png"
                        cell_image.save(cell_filename)
                        
                        print(f"  R1C{col+1}: 已保存 {cell_filename}")
                        
                    except Exception as e:
                        print(f"  R1C{col+1}: 裁剪失败 - {e}")
            
            print(f"\n=== 精细调整完成 ===")
            print("请查看生成的图像文件，找出最佳的边距配置")
            print("重点检查:")
            print("1. 网格线是否与卡片的黑色边框对齐")
            print("2. 中间卡片的右边是否完整")
            print("3. 所有卡片内容是否都被完整包含")
            
    except Exception as e:
        print(f"精细调整失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    page_num = 100
    
    fine_tune_7x3_layout(pdf_path, page_num)

if __name__ == "__main__":
    main()
