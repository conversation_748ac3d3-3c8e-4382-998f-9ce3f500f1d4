#!/usr/bin/env python3
"""
Debug OCR output to analyze the quality of text recognition
"""

import pdfplumber
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
import re

def enhance_image_for_ocr(image):
    """Enhance image quality for better OCR recognition."""
    try:
        from PIL import ImageEnhance, ImageFilter, ImageOps
        import numpy as np
        
        # Convert to grayscale if not already
        if image.mode != 'L':
            image = image.convert('L')
        
        # Resize image if too small (minimum 200px width for better OCR)
        width, height = image.size
        if width < 200:
            scale_factor = 200 / width
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Increase contrast more aggressively
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(3.0)
        
        # Increase brightness slightly
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(1.2)
        
        # Increase sharpness
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(2.5)
        
        # Convert to numpy array for advanced processing
        img_array = np.array(image)
        
        # Apply threshold to create binary image
        threshold = np.mean(img_array)
        img_array = np.where(img_array > threshold, 255, 0).astype(np.uint8)
        
        # Convert back to PIL Image
        image = Image.fromarray(img_array)
        
        # Apply morphological operations to clean up
        image = image.filter(ImageFilter.MedianFilter(size=3))
        
        return image
    except Exception as e:
        print(f"Image enhancement failed: {e}")
        return image

def debug_single_cell(pdf_path, page_num, row, col, rows=3, cols=7):
    """Debug OCR output for a single cell"""
    print(f"\n=== 调试第{page_num}页，第{row}行第{col}列 ===")
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page = pdf.pages[page_num - 1]
            
            # Get page dimensions
            page_width = page.width
            page_height = page.height
            
            # Calculate cell dimensions
            cell_width = page_width / cols
            cell_height = page_height / rows
            
            # Calculate cell boundaries
            x0 = (col - 1) * cell_width
            y0 = (row - 1) * cell_height
            x1 = col * cell_width
            y1 = row * cell_height
            
            print(f"页面尺寸: {page_width} x {page_height}")
            print(f"单元格尺寸: {cell_width} x {cell_height}")
            print(f"单元格坐标: ({x0}, {y0}) -> ({x1}, {y1})")
            
            # Convert page to image
            page_image = page.to_image(resolution=600)
            pil_image = page_image.original
            
            # Convert coordinates to image coordinates
            img_width, img_height = pil_image.size
            img_x0 = int(x0 * img_width / page_width)
            img_y0 = int(y0 * img_height / page_height)
            img_x1 = int(x1 * img_width / page_width)
            img_y1 = int(y1 * img_height / page_height)
            
            print(f"图像尺寸: {img_width} x {img_height}")
            print(f"图像坐标: ({img_x0}, {img_y0}) -> ({img_x1}, {img_y1})")
            
            # Crop the cell area
            cell_image = pil_image.crop((img_x0, img_y0, img_x1, img_y1))
            print(f"裁剪后尺寸: {cell_image.size}")
            
            # Save original cell image for inspection
            cell_image.save(f"debug_cell_original_p{page_num}_r{row}_c{col}.png")
            print(f"已保存原始图像: debug_cell_original_p{page_num}_r{row}_c{col}.png")
            
            # Enhance image
            enhanced_image = enhance_image_for_ocr(cell_image)
            enhanced_image.save(f"debug_cell_enhanced_p{page_num}_r{row}_c{col}.png")
            print(f"已保存增强图像: debug_cell_enhanced_p{page_num}_r{row}_c{col}.png")
            
            # Test different OCR configurations
            configs = [
                ("英文", r'--oem 3 --psm 6 -l eng'),
                ("中文", r'--oem 3 --psm 6 -l chi_sim'),
                ("中英混合", r'--oem 3 --psm 6 -l chi_sim+eng'),
                ("中英混合块", r'--oem 3 --psm 8 -l chi_sim+eng'),
                ("单词模式", r'--oem 3 --psm 13 -l eng'),
            ]
            
            print("\n--- OCR测试结果 ---")
            for name, config in configs:
                try:
                    text = pytesseract.image_to_string(enhanced_image, config=config)
                    text = text.strip()
                    print(f"{name:10}: '{text}'")
                    if text:
                        # 分析文本内容
                        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
                        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
                        print(f"           英文词: {english_words}")
                        print(f"           中文字: {''.join(chinese_chars)}")
                except Exception as e:
                    print(f"{name:10}: 错误 - {e}")
            
            # Try traditional text extraction
            try:
                cell = page.crop((x0, y0, x1, y1))
                traditional_text = cell.extract_text()
                print(f"\n传统提取    : '{traditional_text}'")
            except Exception as e:
                print(f"\n传统提取    : 错误 - {e}")
                
    except Exception as e:
        print(f"调试失败: {e}")

def main():
    pdf_path = "data/flashcards.pdf"
    
    # 测试几个不同的单元格
    test_cells = [
        (100, 1, 2),  # 第100页，第1行第2列
        (100, 1, 3),  # 第100页，第1行第3列
        (100, 2, 4),  # 第100页，第2行第4列
    ]
    
    for page_num, row, col in test_cells:
        debug_single_cell(pdf_path, page_num, row, col)
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
